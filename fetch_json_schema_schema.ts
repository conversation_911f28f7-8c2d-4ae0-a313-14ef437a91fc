import { dereference } from "@apidevtools/json-schema-ref-parser";
import { writeFile } from "fs/promises";
import { JSONSchema } from "json-schema-typed";
import fetch from "node-fetch";
import { hv, jsonSchema2020Url, pretty } from "~/Tools";

// Supposedly only necessary for node < 18,but doesn't seem to work on 22, ref parser spits out garbage otherwise
// see https://github.com/APIDevTools/json-schema-ref-parser?tab=readme-ov-file#polyfills
// eslint-disable-next-line @typescript-eslint/no-explicit-any
(globalThis as any).fetch = fetch;

const schema = await dereference(
  {
    type: "object",
    properties: {
      schema: {
        $id: "extended-schema",
        $ref: jsonSchema2020Url,
        type: "object",
        properties: {
          format: {
            type: "string",
            enum: [
              "date",
              "time",
              "date-time",
              "iso-time",
              "iso-date-time",
              "duration",
              "uri",
              "email",
              "int32",
              "int64",
              "float",
              "double",
            ],
          },
        },
      },
    },
    required: ["schema"],
  } satisfies JSONSchema,
  {
    dereference: {
      circular: false,
      onDereference: (_ref: string, value: JSONSchema & object) => {
        if (hv(value.$id) && value.$id.includes("https://json-schema.org")) {
          // remove fields that confuse Ajv
          delete value.$id;
          delete value.$dynamicAnchor;
        }
      },
    },
  },
);
await writeFile(
  "app/utils/extended-json-schema-schema.json",
  pretty(schema),
  "utf8",
);

export {};
