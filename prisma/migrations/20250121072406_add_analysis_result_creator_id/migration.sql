-- CreateEnum
CREATE TYPE "SubmissionSource" AS ENUM ('API', 'USER');

-- AlterTable
ALTER TABLE "AnalysisResult" ADD COLUMN     "creatorId" TEXT,
ADD COLUMN     "submissionSource" "SubmissionSource" NULL;

update "AnalysisResult"
set "submissionSource" = 'API';

alter table "AnalysisResult" alter column "submissionSource" set not null;

-- AddForeignKey
ALTER TABLE "AnalysisResult" ADD CONSTRAINT "AnalysisResult_creatorId_fkey" FOREIGN KEY ("creatorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
