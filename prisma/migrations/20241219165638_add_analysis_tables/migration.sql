/*
  Warnings:

  - You are about to drop the `Rule` table. If the table is not empty, all the data it contains will be lost.

*/
-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "RecommendedAction" AS ENUM ('APPROVE', 'DECLINE', 'RAISE_PREMIUM', 'RU<PERSON>_EVALUATION', 'CONTENT_EVALUATION', 'RISK_EVALUATION', 'OTHER_EVALUATION');

-- <PERSON>reateEnum
CREATE TYPE "ContentType" AS ENUM ('ADVERSARIAL', 'RULE_MATCH', 'UNMATCHED_RISK', 'INSCRUTABLE');

-- DropTable
DROP TABLE "Rule";

-- CreateTable
CREATE TABLE "AnalysisResult" (
    "id" TEXT NOT NULL DEFAULT '',
    "programId" TEXT NOT NULL,
    "programText" TEXT NOT NULL,
    "underwritingContent" TEXT NOT NULL,
    "systemPromptVersion" TEXT NOT NULL,
    "developerMessage" TEXT,
    "recommendedAction" "RecommendedAction" NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AnalysisResult_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "AnalysisLineItem" (
    "id" TEXT NOT NULL DEFAULT '',
    "analysisResultId" TEXT NOT NULL,
    "contentType" "ContentType" NOT NULL,
    "reasoningText" TEXT NOT NULL,
    "textSegments" TEXT[],
    "confidencePct" SMALLINT NOT NULL,
    "riskPct" SMALLINT,
    "versionedRuleId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "AnalysisLineItem_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_AnalysisResultToVersionedRule" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "_AnalysisResultToVersionedRule_AB_unique" ON "_AnalysisResultToVersionedRule"("A", "B");

-- CreateIndex
CREATE INDEX "_AnalysisResultToVersionedRule_B_index" ON "_AnalysisResultToVersionedRule"("B");

-- AddForeignKey
ALTER TABLE "AnalysisLineItem" ADD CONSTRAINT "AnalysisLineItem_analysisResultId_fkey" FOREIGN KEY ("analysisResultId") REFERENCES "AnalysisResult"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AnalysisLineItem" ADD CONSTRAINT "AnalysisLineItem_versionedRuleId_fkey" FOREIGN KEY ("versionedRuleId") REFERENCES "VersionedRule"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AnalysisResultToVersionedRule" ADD CONSTRAINT "_AnalysisResultToVersionedRule_A_fkey" FOREIGN KEY ("A") REFERENCES "AnalysisResult"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AnalysisResultToVersionedRule" ADD CONSTRAINT "_AnalysisResultToVersionedRule_B_fkey" FOREIGN KEY ("B") REFERENCES "VersionedRule"("id") ON DELETE CASCADE ON UPDATE CASCADE;
