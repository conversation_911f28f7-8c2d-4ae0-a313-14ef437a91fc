/*
  Warnings:

  - The values [PASS,FAIL] on the enum `RuleCategory` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "RuleCategory_new" AS ENUM ('INCLUDE', 'EXCLUDE', 'FLAT_PREMIUM', 'PERCENT_PREMIUM', 'REQUIRES_UNDERWRITER_REVIEW');
ALTER TABLE "Rule" ALTER COLUMN "category" TYPE "RuleCategory_new" USING ("category"::text::"RuleCategory_new");
ALTER TYPE "RuleCategory" RENAME TO "RuleCategory_old";
ALTER TYPE "RuleCategory_new" RENAME TO "RuleCategory";
DROP TYPE "RuleCategory_old";
COMMIT;
