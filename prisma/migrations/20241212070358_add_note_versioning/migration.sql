-- CreateTable
CREATE TABLE "VersionedRule" (
    "id" TEXT NOT NULL DEFAULT '',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "category" "RuleCategory" NOT NULL,
    "text" TEXT NOT NULL,
    "basisValue" INTEGER NOT NULL,
    "ruleMetadataId" TEXT NOT NULL,
    "creatorId" TEXT NOT NULL,

    CONSTRAINT "VersionedRule_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "RuleMetadata" (
    "id" TEXT NOT NULL DEFAULT '',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "RuleMetadata_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL DEFAULT '',
    "externalId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_externalId_key" ON "User"("externalId");

-- AddForeignKey
ALTER TABLE "VersionedRule" ADD CONSTRAINT "VersionedRule_ruleMetadataId_fkey" FOREIGN KEY ("ruleMetadataId") REFERENCES "RuleMetadata"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "VersionedRule" ADD CONSTRAINT "VersionedRule_creatorId_fkey" FOREIGN KEY ("creatorId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
