-- AddFore<PERSON><PERSON>ey
ALTER TABLE "VersionedRule" ADD CONSTRAINT "VersionedRule_ruleMetadataId_fkey" FOREIGN KEY ("ruleMetadataId") REFERENCES "RuleMetadata"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddFore<PERSON>Key
ALTER TABLE "VersionedRule" ADD CONSTRAINT "VersionedRule_creatorId_fkey" FOREIGN KEY ("creatorId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "RuleMetadata" ADD CONSTRAINT "RuleMetadata_programId_fkey" FOREIGN KEY ("programId") REFERENCES "Program"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AnalysisResult" ADD CONSTRAINT "AnalysisResult_creatorId_fkey" FOREI<PERSON><PERSON>EY ("creatorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AnalysisLineItem" ADD CONSTRAINT "AnalysisLineItem_analysisResultId_fkey" FOREIGN KEY ("analysisResultId") REFERENCES "AnalysisResult"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "AnalysisLineItem" ADD CONSTRAINT "AnalysisLineItem_versionedRuleId_fkey" FOREIGN KEY ("versionedRuleId") REFERENCES "VersionedRule"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Program" ADD CONSTRAINT "Program_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Chat" ADD CONSTRAINT "Chat_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChatMessage" ADD CONSTRAINT "ChatMessage_chatId_fkey" FOREIGN KEY ("chatId") REFERENCES "Chat"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AnalysisResultToVersionedRule" ADD CONSTRAINT "_AnalysisResultToVersionedRule_A_fkey" FOREIGN KEY ("A") REFERENCES "AnalysisResult"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_AnalysisResultToVersionedRule" ADD CONSTRAINT "_AnalysisResultToVersionedRule_B_fkey" FOREIGN KEY ("B") REFERENCES "VersionedRule"("id") ON DELETE CASCADE ON UPDATE CASCADE;
