-- CreateEnum
CREATE TYPE "PercentMultiplierType" AS ENUM ('BASE_PREMIUM', 'TOTAL_REVENUES', 'PARTICIPANT_FEES', 'EQUIPMENT_COSTS');

-- CreateEnum
CREATE TYPE "FlatMultiplierType" AS ENUM ('PER_PARTICIPANT', 'PER_DAY', 'PER_WEEK', 'ONE_TIME');

-- AlterTable
ALTER TABLE "VersionedRule" ADD COLUMN     "flatMultiplierType" "FlatMultiplierType",
ADD COLUMN     "percentMultiplierType" "PercentMultiplierType";

update "VersionedRule"
set "flatMultiplierType" = 'ONE_TIME'
where category = 'FLAT_PREMIUM';

update "VersionedRule"
set "percentMultiplierType" = 'BASE_PREMIUM'
where category = 'PERCENT_PREMIUM';