/*
  Warnings:

  - The primary key for the `AnalysisLineItem` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to alter the column `id` on the `AnalysisLineItem` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(6)`.
  - The primary key for the `AnalysisResult` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to alter the column `id` on the `AnalysisResult` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(6)`.
  - The primary key for the `Chat` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to alter the column `id` on the `Chat` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(10)`.
  - The primary key for the `Organization` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to alter the column `id` on the `Organization` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(6)`.
  - The primary key for the `Program` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to alter the column `id` on the `Program` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(6)`.
  - The primary key for the `RuleMetadata` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to alter the column `id` on the `RuleMetadata` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(6)`.
  - The primary key for the `User` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to alter the column `id` on the `User` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(6)`.
  - The primary key for the `VersionedRule` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to alter the column `id` on the `VersionedRule` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(6)`.
  - The primary key for the `_AnalysisResultToVersionedRule` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to alter the column `A` on the `_AnalysisResultToVersionedRule` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(6)`.
  - You are about to alter the column `B` on the `_AnalysisResultToVersionedRule` table. The data in that column could be lost. The data in that column will be cast from `Text` to `VarChar(6)`.

*/
-- DropForeignKey
ALTER TABLE "_AnalysisResultToVersionedRule" DROP CONSTRAINT "_AnalysisResultToVersionedRule_A_fkey";

-- DropForeignKey
ALTER TABLE "_AnalysisResultToVersionedRule" DROP CONSTRAINT "_AnalysisResultToVersionedRule_B_fkey";

-- AlterTable
ALTER TABLE "AnalysisLineItem" DROP CONSTRAINT "AnalysisLineItem_pkey" cascade,
ALTER COLUMN "id" SET DATA TYPE VARCHAR(6),
ADD CONSTRAINT "AnalysisLineItem_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "AnalysisResult" DROP CONSTRAINT "AnalysisResult_pkey" cascade,
ALTER COLUMN "id" SET DATA TYPE VARCHAR(6),
ADD CONSTRAINT "AnalysisResult_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Chat" DROP CONSTRAINT "Chat_pkey" cascade,
ALTER COLUMN "id" SET DATA TYPE VARCHAR(10),
ADD CONSTRAINT "Chat_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Organization" DROP CONSTRAINT "Organization_pkey" cascade,
ALTER COLUMN "id" SET DATA TYPE VARCHAR(6),
ADD CONSTRAINT "Organization_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "Program" DROP CONSTRAINT "Program_pkey" cascade,
ALTER COLUMN "id" SET DATA TYPE VARCHAR(6),
ADD CONSTRAINT "Program_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "RuleMetadata" DROP CONSTRAINT "RuleMetadata_pkey" cascade,
ALTER COLUMN "id" SET DATA TYPE VARCHAR(6),
ADD CONSTRAINT "RuleMetadata_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "User" DROP CONSTRAINT "User_pkey" cascade,
ALTER COLUMN "id" SET DATA TYPE VARCHAR(6),
ADD CONSTRAINT "User_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "VersionedRule" DROP CONSTRAINT "VersionedRule_pkey" cascade,
ALTER COLUMN "id" SET DATA TYPE VARCHAR(6),
ADD CONSTRAINT "VersionedRule_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "_AnalysisResultToVersionedRule" DROP CONSTRAINT "_AnalysisResultToVersionedRule_AB_pkey" cascade,
ALTER COLUMN "A" SET DATA TYPE VARCHAR(6),
ALTER COLUMN "B" SET DATA TYPE VARCHAR(6),
ADD CONSTRAINT "_AnalysisResultToVersionedRule_AB_pkey" PRIMARY KEY ("A", "B");