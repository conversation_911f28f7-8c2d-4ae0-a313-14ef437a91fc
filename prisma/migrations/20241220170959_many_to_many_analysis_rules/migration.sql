/*
  Warnings:

  - You are about to drop the column `versionedRuleIds` on the `AnalysisResult` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "AnalysisResult" DROP COLUMN "versionedRuleIds";

-- CreateTable
CREATE TABLE "_AnalysisResultToVersionedRule" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_AnalysisResultToVersionedRule_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_AnalysisResultToVersionedRule_B_index" ON "_AnalysisResultToVersionedRule"("B");

-- AddForeignKey
ALTER TABLE "_AnalysisResultToVersionedRule" ADD CONSTRAINT "_AnalysisResultToVersionedRule_A_fkey" FOREIGN KEY ("A") REFERENCES "AnalysisResult"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddF<PERSON>ignKey
ALTER TABLE "_AnalysisResultToVersionedRule" ADD CONSTRAINT "_AnalysisResultToVersionedRule_B_fkey" FOREIGN KEY ("B") REFERENCES "VersionedRule"("id") ON DELETE CASCADE ON UPDATE CASCADE;
