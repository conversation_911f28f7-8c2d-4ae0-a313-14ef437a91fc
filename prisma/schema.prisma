// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

// NOTE: ALL ids are marked as default with a clearly colliding string,
// this is because they're filled by middleware and we want prisma's create to mark id as optional
// and we want the create to fail if the id is somehow not filled
// ideally we'd investigate something like @dbgenerated("always_crashes") so it also errors on the first attempt
// we're using a custom alphabet for nanoid so we're not using @default(nanoid()) for now

generator client {
  provider = "prisma-client-js"
}

generator typesOnly {
  provider  = "prisma-generator-typescript-interfaces"
  enumType  = "object"
  modelType = "type"
}

generator json {
  provider = "prisma-json-types-generator"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum RuleCategory {
  EXCLUDE
  FLAT_PREMIUM
  INCLUDE
  PERCENT_PREMIUM
  REQUIRES_UNDERWRITER_REVIEW
}

enum RecommendedAction {
  APPROVE
  CONTENT_EVALUATION
  DECLINE
  OTHER_EVALUATION
  RAISE_PREMIUM
  RISK_EVALUATION
  RULE_EVALUATION
}

enum ContentType {
  ADVERSARIAL
  CONNECTIVE
  INSCRUTABLE
  RULE_MATCH
  UNMATCHED_RISK
}

enum SubmissionSource {
  API
  USER
}

enum PercentMultiplierType {
  BASE_PREMIUM
  TOTAL_REVENUES
  PARTICIPANT_FEES
  EQUIPMENT_COSTS
}

enum FlatMultiplierType {
  PER_PARTICIPANT
  PER_DAY
  PER_WEEK
  ONE_TIME
}

model VersionedRule {
  id                    String                 @id @default("") @db.VarChar(6)
  createdAt             DateTime               @default(now())
  category              RuleCategory
  text                  String
  basisValue            Int
  ruleMetadata          RuleMetadata           @relation(fields: [ruleMetadataId], references: [id])
  ruleMetadataId        String
  creator               User                   @relation(fields: [creatorId], references: [id])
  creatorId             String
  analysisLineItems     AnalysisLineItem[]
  analysisResults       AnalysisResult[]
  percentMultiplierType PercentMultiplierType?
  flatMultiplierType    FlatMultiplierType?
}

model RuleMetadata {
  id        String          @id @default("") @db.VarChar(6)
  createdAt DateTime        @default(now())
  deletedAt DateTime?
  versions  VersionedRule[]
  program   Program         @relation(fields: [programId], references: [id])
  programId String
}

model User {
  id             String           @id @default("") @db.VarChar(6)
  clerkId        String           @unique
  createdAt      DateTime         @default(now())
  versionedRule  VersionedRule[]
  AnalysisResult AnalysisResult[]
  Chat           Chat[]
}

model AnalysisResult {
  id                   String             @id @default("") @db.VarChar(6)
  programId            String
  programName          String
  programDescription   String?
  underwritingContent  String
  systemPromptVersion  String
  developerMessage     String?
  recommendedAction    RecommendedAction
  overallAiReasoning   String?
  overallConfidencePct Int                @db.SmallInt
  versionedRules       VersionedRule[]
  lineItems            AnalysisLineItem[]
  createdAt            DateTime           @default(now())
  creator              User?              @relation(fields: [creatorId], references: [id])
  creatorId            String?
  submissionSource     SubmissionSource
}

model AnalysisLineItem {
  id               String         @id @default("") @db.VarChar(6)
  analysisResult   AnalysisResult @relation(fields: [analysisResultId], references: [id])
  analysisResultId String
  contentType      ContentType
  reasoningText    String
  textSegments     String[]       @db.Text
  confidencePct    Int            @db.SmallInt
  riskPct          Int?           @db.SmallInt
  versionedRule    VersionedRule? @relation(fields: [versionedRuleId], references: [id])
  versionedRuleId  String?
  createdAt        DateTime       @default(now())
}

model Program {
  id              String         @id @default("") @db.VarChar(6)
  createdAt       DateTime       @default(now())
  name            String
  descriptionText String?
  ruleMetadatas   RuleMetadata[]
  organization    Organization   @relation(fields: [organizationId], references: [id])
  organizationId  String
}

model Organization {
  id        String    @id @default("") @db.VarChar(6)
  createdAt DateTime  @default(now())
  clerkId   String    @unique
  name      String
  program   Program[]
}

model Chat {
  id           String        @id @default("") @db.VarChar(10)
  createdAt    DateTime      @default(now())
  user         User?         @relation(fields: [userId], references: [id])
  userId       String?
  messages     ChatMessage[]
  closedReason String?
}

model ChatMessage {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  chat      Chat     @relation(fields: [chatId], references: [id])
  chatId    String
  /// [ClaudeMessageWithId]
  message   Json
  ipAddress String?
  userId    String?
}
