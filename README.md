# UZero

## Development

Run the dev server:

```shellscript
npm run dev
```

or

```shellscript
npx react-router dev
```

if your browser is already open

Since the prisma client is in node_modules, after running prisma generate you will need to restart vite in order for the server to update with the new files.

## Prisma

### Create new migrations from schema:

```shellscript
npx prisma migrate dev --name <migration_name>
```

## After pulling new commits

Run

```shellscript
npx prisma migrate dev
```

to sync database with current schema

Run

```shellscript
npx typegen
```

to generate prisma and react-router types

## Common errors and known issues

- If form submit is failing without any visible cause, log form.formState.errors
- If enums are showing up on the client side as undefined, clear node_modules/.vite/deps

## Other troubleshooting
- Error: No route matches URL "/installHook.js.map" - _try disabling React Dev Tools extension.

## Markdown highlighting
For any `/*md*/` strings:
1. The final backtick must be _right-aligned and on its own line_
2. Any line indented at least 4 spaces will be interpreted as code (except nested lists)

## Tailwind

We have a dependency on tailwind for now since there are some legacy components that depend on it, but it shouldn't be used going forward.
However by default tailwind destroys the default styling for many things required in markdown formatted text (e.g. lists), so @tailwindcss/typography is needed for that to render properly.

# Troubleshooting isolated-vm
If you run the app and see in console: `Could not resolve "./out/isolated_vm"`
Then run: `npm rebuild isolated-vm`


# Known vite issues
If you see this error
```
Pre-transform error: Server-only module referenced by client

...

React Router automatically removes server-code from these exports:
`loader`, `action`, `headers`

But other route exports in ... depend on ...

See https://remix.run/docs/en/main/guides/vite#splitting-up-client-and-server-code
```
But there are no offending exports, check if you have any corecursive functions
This likely confuses vite's dependency checker

# Env vars
When changing client side env vars on heroku (VITE_), the client needs to be rebuilt in order for the changes to take effect