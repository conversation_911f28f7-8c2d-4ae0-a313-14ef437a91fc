{"name": "uzero", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "prisma generate && react-router build", "dev": "concurrently \"react-router dev\" \"wait-on http://localhost:5173 && open http://localhost:5173\"", "dev-COMMENT": "This is a bit complex but ensures site is available before opening, while also providing logs in terminal", "lint": "eslint -c .commit.eslintrc.cjs --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint --ext .ts,.tsx --fix", "start": "react-router-serve ./build/server/index.js", "heroku-start": "npm run start", "typecheck": "tsc --project tsconfig.json", "pre-commit": "lint-staged", "check-pre-commit": "run-script-os", "check-pre-commit:win32": "git-branch-is --quiet --regex \"(^pj|^PJ|^DP|^deploy|^main$)\" && npm run pre-commit || echo \"** Skipping pre-commit checks **\\n\"", "check-pre-commit:default": "if git-branch-is --quiet --regex '(^main$|^PJ|^DP|^deploy)'; then npm run pre-commit; else echo \"** Skipping pre-commit checks **\\n\"; fi", "typegen": "prisma generate && react-router typegen", "create-migration": "prisma migrate dev --create-only", "migrate": "prisma migrate dev", "gen-schema-schema": "tsx fetch_json_schema_schema.ts", "send-markdown-example": "tsx --env-file=.env sendMarkdownExample.ts", "test": "vitest"}, "dependencies": {"@anthropic-ai/sdk": "^0.50.3", "@clerk/react-router": "^0.2.3", "@clerk/themes": "^2.2.3", "@conform-to/zod": "^1.2.2", "@fortawesome/fontawesome-svg-core": "^6.7.1", "@fortawesome/free-regular-svg-icons": "^6.7.1", "@fortawesome/free-solid-svg-icons": "^6.7.1", "@fortawesome/react-fontawesome": "^0.2.2", "@hookform/resolvers": "^3.9.1", "@langchain/anthropic": "^0.3.11", "@langchain/openai": "^0.3.16", "@prisma/client": "^6.4.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-primitive": "^2.0.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/themes": "^3.1.6", "@react-router/fs-routes": "^7.6.1", "@react-router/node": "^7.6.1", "@react-router/serve": "^7.6.1", "@uidotdev/usehooks": "^2.4.1", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "esbuild-wasm": "^0.25.2", "husky": "^3.1.0", "isbot": "^4.1.0", "isolated-vm": "^5.0.4", "json-schema-typed": "^8.0.1", "lint-staged": "^15.2.10", "lucide-react": "^0.456.0", "marked": "^15.0.12", "nanoid": "^5.0.8", "node-fetch": "^3.3.2", "openai": "^4.78.0", "pg": "^8.13.1", "posthog-js": "^1.254.0", "postmark": "^4.0.5", "prisma-extension-nested-operations": "^1.0.1", "prisma-json-types-generator": "^3.4.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.53.2", "react-markdown": "^9.1.0", "react-router": "^7.6.1", "remark-gfm": "^4.0.1", "run-script-os": "^1.1.6", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "temporal-polyfill": "^0.3.0", "uuid": "^11.1.0", "zod": "^3.24.1", "zod-to-json-schema": "^3.24.1"}, "devDependencies": {"@apidevtools/json-schema-ref-parser": "^11.9.3", "@babel/preset-env": "^7.26.9", "@react-router/dev": "^7.1.3", "@tailwindcss/typography": "^0.5.16", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "autoprefixer": "^10.4.19", "eslint": "^8.38.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-unused-imports": "^4.1.4", "git-branch-is": "^4.0.0", "json-schema-to-ts": "^3.1.1", "postcss": "^8.4.38", "prisma": "^6.4.1", "prisma-generator-typescript-interfaces": "^1.7.0", "tailwindcss": "^3.4.4", "tsx": "^4.19.3", "typescript": "^5.1.6", "vite": "^5.1.0", "vite-tsconfig-paths": "^4.2.1", "vitest": "^3.0.7"}, "prettier": {"arrowParens": "avoid", "trailingComma": "all"}, "engines": {"node": ">=20 <23"}, "husky": {"hooks": {"pre-commit": "env NODE_SKIP_PLATFORM_CHECK=1 npm run check-pre-commit"}}}