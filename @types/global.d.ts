import type { ExtendedPrismaClient } from "~/utils/db.server";

declare global {
  // eslint-disable-next-line no-var
  var __db_hidden: ExtendedPrismaClient | undefined;

  interface ObjectConstructor {
    keys<T>(obj: T): (keyof T)[];
    values<T>(obj: T): T[keyof T][];
    entries<T>(obj: T): [keyof T, T[keyof T]][];
    fromEntries<K extends PropertyKey, V>(
      entries: Iterable<readonly [K, V]>,
    ): { [k in K]: V };
  }
}
