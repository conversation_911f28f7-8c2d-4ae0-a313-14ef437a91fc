import { StorQuickEstimateData } from "~/StorInsureProgram/StorSchema";

export function calculateStorQuickEstimate(
  data: StorQuickEstimateData,
): string {
  // Base rate per $1000 of building coverage
  const baseRatePerThousand = 3.75;

  // Calculate base premium from building coverage
  const basePremium = (data.buildingCoverage / 1000) * baseRatePerThousand;

  // Administrative fees from original function
  const policyFee = 150;
  const inspectionFee = data.unitCount > 100 ? 275 : 175;

  // Total premium cost
  const totalPremium = basePremium + policyFee + inspectionFee;

  // Format currency (same as original but with 0 decimals)
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return /*md*/ `
## StorInsure Quick Estimate

- Total Number of Units: ${formatCurrency(data.unitCount)}
- Building Coverage: ${formatCurrency(data.buildingCoverage)}
- **Total Estimated Cost:** ${formatCurrency(totalPremium)}

_Complete our application to include additional factors that may adjust your premium._
`;
}
