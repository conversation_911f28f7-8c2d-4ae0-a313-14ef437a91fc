import type {
  FullCodeValidationResult,
  SingleCodeValidationResult,
} from "~/Tools";
import { StorApplication } from "~/StorInsureProgram/StorSchema";

export function validateStorPrivateChecks(
  app: StorApplication,
): FullCodeValidationResult {
  const results: SingleCodeValidationResult[] = [
    validateExcludedZipCodes(app.zipCode),
    validateBlacklistedBusinessNames(app.businessName),
    validateFlaggedEmailDomains(app.email),
  ];

  const isEverythingValid = results.every(r => r.isValid);

  return { isEverythingValid, results };
}

export function validateExcludedZipCodes(
  zipCode: string,
): SingleCodeValidationResult {
  // Check if ZIP code is in the excluded range (10000-10009)
  const zipCodeNum = parseInt(zipCode, 10);
  const isExcludedZipCode = zipCodeNum >= 10000 && zipCodeNum <= 10009;

  return {
    isValid: !isExcludedZipCode,
    detailedErrorMessage: isExcludedZipCode
      ? `Application declined - location not eligible for coverage (Zip Code ${zipCode})`
      : null,
  };
}

export function validateBlacklistedBusinessNames(
  businessName: string,
): SingleCodeValidationResult {
  // Check if business name contains blacklisted text "SCMR" (case insensitive)
  const blacklistedTerm = "scmr";
  const businessNameLower = businessName.toLowerCase();
  const containsBlacklistedTerm = businessNameLower.includes(blacklistedTerm);

  return {
    isValid: !containsBlacklistedTerm,
    detailedErrorMessage: containsBlacklistedTerm
      ? `Application requires additional review - business name matches: ${blacklistedTerm}`
      : null,
  };
}

export function validateFlaggedEmailDomains(
  email: string,
): SingleCodeValidationResult {
  // Check if email is from banned domain "sspcs.com"
  const bannedDomain = "sspcs.com";
  const emailDomain = email.split("@")[1]?.toLowerCase();
  const isFlaggedDomain = emailDomain === bannedDomain;

  return {
    isValid: !isFlaggedDomain,
    detailedErrorMessage: isFlaggedDomain
      ? `Application declined - email domain not accepted (${bannedDomain})`
      : null,
  };
}
