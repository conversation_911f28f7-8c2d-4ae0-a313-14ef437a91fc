import { StorQuickEstimateData } from "~/StorInsureProgram/StorSchema";
import { runIsolatedCode } from "~/utils/isolate.server";
import outputCodeTs from "../StorInsureProgram/storQuickEstimate.ts?raw";

export async function isolateStorQuickEstimate(data: StorQuickEstimateData) {
  console.log("ISOLATING Stor quick estimate...");

  const result = await runIsolatedCode<string>(
    outputCodeTs,
    "calculateStorQuickEstimate",
    data,
  );

  console.log(`=== DONE with ISOLATED estimate.`);

  return result;
}
