import { StorApplication } from "~/StorInsureProgram/StorSchema";
import { runIsolatedCode } from "~/utils/isolate.server";
import outputCodeTs from "../StorInsureProgram/storProgramOutput.ts?raw";

export async function isolateAppOutput(app: StorApplication) {
  console.log("ISOLATING Stor app output...");

  const result = await runIsolatedCode<string>(
    outputCodeTs,
    "calculateStorInsurancePremium",
    app,
  );

  console.log(`=== DONE with ISOLATED OUTPUT.`);
  return result;
}
