/* Note: need to restart vite after changing this file */

import { User } from "@clerk/react-router/ssr.server";
import {
  AnalysisLineItem,
  AnalysisResult,
  Prisma,
  Program,
  VersionedRule,
} from "@prisma/client";
import { hasValueFn, hv, no } from "~/Tools";
import { prisma } from "./db.server";
import {
  ExtendsStructureOf,
  selectAll,
  selectStructure,
  Structure,
} from "./selectFields";
import { annotateUnderwritingContent } from "./analysisHelpers.server";

export const modelExtension = Prisma.defineExtension({
  name: "model extension",
  model: {
    $allModels: {
      async exists<T>(this: T, where: Prisma.Args<T, "findFirst">["where"]) {
        const context = Prisma.getExtensionContext(this);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const result = await (context as any).findFirst({ where });
        return result !== null;
      },
    },
    program: {
      async fetchWithCurrentVersionedRules(
        id: string,
        auth: { orgId: string },
      ) {
        const program = await prisma.program.findFirst({
          where: {
            id,
            organization: { clerkId: auth.orgId },
          },
          include: {
            ruleMetadatas: {
              include: {
                versions: {
                  orderBy: { createdAt: "desc" },
                  take: 1,
                },
              },
            },
          },
        });
        if (!hv(program)) {
          return null;
        }
        return {
          ...program,
          currentVersionedRules: program.ruleMetadatas.map(
            m => m.versions[0] ?? no(),
          ),
        };
      },
      async fetchWithCurrentVersionedRulesOrThrow(
        id: string,
        auth: { orgId: string },
      ) {
        return (await this.fetchWithCurrentVersionedRules(id, auth)) ?? no();
      },
    },
  },
});

function getSelectorFn<S extends Structure>(structure: S) {
  function f<B>(this: B) {
    return selectStructure(this, structure);
  }
  f.structure = structure;
  return f;
}

export const dbTypeFieldName = "__dbObject";

export const resultExtension = Prisma.defineExtension({
  name: "result extension",
  result: {
    $allModels: {
      [dbTypeFieldName]: {
        compute: () => true,
      },
    },

    user: {
      getClientFields: {
        compute() {
          return getSelectorFn({
            id: true,
            clerkId: true,
          } satisfies ExtendsStructureOf<User>);
        },
      },
    },

    program: {
      getClientFields: {
        compute() {
          return getSelectorFn({
            id: true,
            name: true,
            descriptionText: true,
            currentVersionedRules: true,
          } satisfies ExtendsStructureOf<Program>);
        },
      },
    },

    versionedRule: {
      getClientFields: {
        compute() {
          return getSelectorFn({
            id: true,
            category: true,
            text: true,
            basisValue: true,
            ruleMetadataId: true,
            flatMultiplierType: true,
            percentMultiplierType: true,
          } satisfies ExtendsStructureOf<VersionedRule>);
        },
      },
    },

    analysisLineItem: {
      getClientFields: {
        compute() {
          return getSelectorFn({
            id: true,
            contentType: true,
            reasoningText: true,
            textSegments: true,
            confidencePct: true,
            riskPct: true,
            versionedRuleId: true,
          } satisfies ExtendsStructureOf<AnalysisLineItem>);
        },
      },
    },

    analysisResult: {
      annotatedUnderwritingContent: {
        needs: { underwritingContent: true },
        compute(obj) {
          if (!("lineItems" in obj)) {
            return [
              { match: obj.underwritingContent, index: 0, lineItemId: null },
            ];
          }
          return annotateUnderwritingContent(
            obj.lineItems as AnalysisLineItem[],
            obj.underwritingContent,
          );
        },
      },

      getClientFields: {
        compute() {
          return getSelectorFn({
            id: true,
            createdAt: true,
            overallAiReasoning: true,
            overallConfidencePct: true,
            programId: true,
            programName: true,
            programDescription: true,
            recommendedAction: true,
            systemPromptVersion: true,
            underwritingContent: true,
            annotatedUnderwritingContent: true,
            lineItems: true,
            submissionSource: true,
            creator: true,
            versionedRules: { [selectAll]: { id: true } },
          } satisfies ExtendsStructureOf<AnalysisResult>);
        },
      },
    },
    chat: {
      isClosed: {
        needs: { closedReason: true },
        compute(chat) {
          return hv(chat.closedReason);
        },
      },
    },
  },
});

export function whereAnalysisResultIsUserOrApi(clerkAuth: { userId: string }) {
  return {
    OR: [
      { submissionSource: "API" },
      {
        submissionSource: "USER",
        creator: { clerkId: clerkAuth.userId },
      },
    ],
  } satisfies Prisma.AnalysisResultWhereInput;
}

export function whereChatId(
  id: string,
  clerkAuth?: {
    userId: string | null;
  },
) {
  return {
    id,
    OR: [
      hasValueFn(clerkAuth?.userId, clerkId => ({ user: { clerkId } })),
      { user: null },
    ].filter(hv),
  } satisfies Prisma.ChatWhereInput;
}
