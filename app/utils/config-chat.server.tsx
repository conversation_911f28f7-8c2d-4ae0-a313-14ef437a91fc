import { Tool } from "@anthropic-ai/sdk/resources/index.mjs";
import { z } from "zod";
import { zodToJsonSchema } from "zod-to-json-schema";
import { hasValueFn, hv, zodToJsonSchema2020 } from "~/Tools";
import {
  CURRENT_PRODUCER_CONFIG,
  devSupportEmail,
  PRODUCER_CONFIGS,
  ProducerConfig,
} from "~/producers";
import { ProgramConfig } from "~/programs.server";
import { AiChatConfig } from "~/utils/chatTools";
import {
  readConciergePrompt,
  readJsonSchemaPrompt,
  readNatLangApplicantPrompt,
  readRequirementsCodeSystemPrompt,
  readRequirementsDocumentSystemPrompt,
} from "~/utils/systemPrompts";
import { zx } from "~/utils/validationHelpers";
import { CITADEL_PROGRAM_NAMES } from "./citadelProgramData";

export const aiDisclaimer = `_Note: The Concierge is still under development. AI can make mistakes. You can **enter feedback directly in the chat**, or contact **${devSupportEmail}**_.`;

const requirementsDocumentSchema = z.object({
  requirementsDocumentMd: z.string().describe("Use markdown"),
});

const submitRequirementsDocumentTool: Tool = {
  name: "submit_requirements_document",
  input_schema: zodToJsonSchema(requirementsDocumentSchema) as Tool.InputSchema,
  description: /*md*/ `
- Calling this tool ends the conversation; do not include an accompanying text message, since User will not receive it
`,
};

const requirementsCodeSchema = z.object({
  finalCodeText: zx.saneString(),
});

const submitRequirementsCodeTool: Tool = {
  name: "submit_final_code",
  input_schema: zodToJsonSchema(requirementsCodeSchema) as Tool.InputSchema,
  description: /*md*/ `
- Call this function after your test results have been confirmed by the user
- Calling this function completes the process and ends the chat
`,
};

const reqSchemaSchema = z.object({
  requirementsJsonSchema: zx.saneString(),
});

const reqSchemaTool: Tool = {
  name: "submit_json_schema",
  input_schema: zodToJsonSchema(reqSchemaSchema) as Tool.InputSchema,
};

async function _loadReqsDocConfig(args: {
  producer: ProducerConfig;
}): Promise<AiChatConfig> {
  return {
    tools: [submitRequirementsDocumentTool],
    systemPrompt: await readRequirementsDocumentSystemPrompt(args),
    initialAiMessage: `Hello, I'm the ${args.producer.aiName}.

Please provide your initial program requirements, and I will assist in creating a final Requirements Document.
`,
  };
}

async function _loadReqsCodeConfig(args: {
  producer: ProducerConfig;
}): Promise<AiChatConfig> {
  return {
    tools: [submitRequirementsCodeTool],
    systemPrompt: await readRequirementsCodeSystemPrompt(args),
  };
}

async function _loadJsonSchemaConfig(): Promise<AiChatConfig> {
  return {
    tools: [reqSchemaTool],
    systemPrompt: await readJsonSchemaPrompt(),
    tool_choice: { type: "any" }, // Not a chat - must call a tool
  };
}

const performDetailedReviewZodSchema = z.object({
  naturalLanguageApplicationMd: zx.saneString(),
});

export const performDetailedReviewJsonSchema = zodToJsonSchema2020(
  performDetailedReviewZodSchema,
);

export type PerformDetailedReviewInput = z.infer<
  typeof performDetailedReviewZodSchema
>;

export const calculateQuickEstimateToolName = "calculate_quick_estimate";

export const submitForDetailedReview: Tool = {
  name: "submit_for_detailed_review",
  input_schema: performDetailedReviewJsonSchema as Tool.InputSchema,
  description: /*md*/ `Use this tool to perform final, detailed review of the Application
  - You must complete all of your own validations before calling this tool
  - Include all field values in this submission
- Your submitted application should match the Requirements Doc precisely!  Take your time and be very careful
- If you get error results from this tool, you must get new data from the user, then resubmit until this tool does not return errors
`,
};

function createWebSearchTool(allowedDomains: string[]): Tool {
  return {
    name: "web_search",
    type: "web_search_20250305",

    // Optional: Limit the number of searches per request
    // max_uses: 5,

    // Optional: Only include results from these domains
    allowed_domains: allowedDomains.length > 0 ? allowedDomains : undefined,
  } as unknown as Tool;
  // fields for web search are not in SDK. docs: https://docs.anthropic.com/en/docs/build-with-claude/tool-use/web-search-tool
}

const webSearchToolArr: Tool[] =
  CURRENT_PRODUCER_CONFIG.webSearchDomains.length === 0
    ? []
    : [createWebSearchTool(CURRENT_PRODUCER_CONFIG.webSearchDomains)];

async function _loadSandboxConfig(): Promise<AiChatConfig> {
  return {
    systemPrompt:
      "This is a sandbox, debug chat with the developer.  We're testing the Web Search tool.",
    tools: [
      createWebSearchTool([]), //["citadelus.com", "cargoprotect.citadelus.com"]),
    ],
    tool_choice: { type: "tool", name: "web_search" },
  };
}

const cancelAppInputZodSchema = z.object({
  reason: zx.saneS,
});

const cancelAppInputJsonSchema = zodToJsonSchema2020(cancelAppInputZodSchema);

export const cancelApplicationTool: Tool = {
  name: "cancel_application",
  input_schema: cancelAppInputJsonSchema as Tool.InputSchema,
  description: /*md*/ `Cancel the Application.
- If you believe you should cancel, always ASK THE USER to CONFIRM before canceling the application
- You should only ever Cancel the application this way at the user's request
- When you call this tool, do not include any additional text to the user
  - (Bad example: "Ok, I am canceling the application now.")
`,
};

export async function loadNatLangAppConfig(args: {
  producer: ProducerConfig;
  program: ProgramConfig;
}): Promise<AiChatConfig> {
  return {
    tools: [
      submitForDetailedReview,
      hasValueFn(args.program.quickEstimate, q => ({
        name: calculateQuickEstimateToolName,
        input_schema: q?.schema,
      })),
      cancelApplicationTool,
    ].filter(hv),
    systemPrompt: await readNatLangApplicantPrompt(args),
    initialAiMessage: args.program.initialAiMessageMd,
    aiDisclaimer,
  };
}

const startAppInputZodSchema = z.object({
  programName: z.enum(CITADEL_PROGRAM_NAMES),
});

const startAppInputJsonSchema = zodToJsonSchema2020(startAppInputZodSchema);

export const startApplicationTool: Tool = {
  name: "start_application",
  description: /*md*/ `This tool starts an insurance application for one of the provided programs
- ALWAYS ASK THE USER FOR CONFIRMATION before starting the application!
- Good example: User "I have a pet grooming business and I need insurance."  You: "Our PetProtect product was designed for businesses like yours.  Would you like to start an application now?"
- When you call this tool, do not include any additional text to the user
  - (Bad example: "Ok, I am starting the application now.")
`,
  input_schema: startAppInputJsonSchema as Tool.InputSchema,
};

const conciergeProducts = [startApplicationTool];

export async function loadConciergeConfig(args: {
  producer: ProducerConfig;
}): Promise<AiChatConfig> {
  return {
    tools: [...webSearchToolArr, ...conciergeProducts],
    systemPrompt: await readConciergePrompt(args),
    initialAiMessage: `**Hello!** I'm the ${args.producer.aiName}.  I can answer questions about our company and guide you to insurance coverage appropriate to your needs.

We offer an extensive suite of products in transportation, energy, construction, and many other fields.

What can I help you with today?
`,
    aiDisclaimer,
  };
}

export const DEBUG_PROMPT_INSPECTION_MODE: boolean = true;

export async function loadCurrentAiChatConfig(args: {
  producer: ProducerConfig;
  program: ProgramConfig;
}) {
  return await loadConciergeConfig(args);
}

export async function fetchAiChatConfig(
  program: ProgramConfig | undefined,
): Promise<AiChatConfig> {
  if (hv(program)) {
    return await loadNatLangAppConfig({
      producer: PRODUCER_CONFIGS[program.producerId],
      program,
    });
  } else {
    return await loadConciergeConfig({
      producer: PRODUCER_CONFIGS["Citadel"],
    });
  }
}
