import { AssembledEvent, isExpectedContentBlock } from "~/llm/assembleEvents";
import { hv } from "~/Tools";
import { prisma } from "./db.server";

export async function* saveToDb(
  events: AsyncGenerator<AssembledEvent>,
  args: {
    chatId: string;
    userId?: string | null;
    ipAddress?: string | null;
  },
): AsyncGenerator<AssembledEvent> {
  let finalMessages: AssembledEvent["messages"] = [];
  let closeSessionReason: string | null = null;
  for await (const ev of events) {
    finalMessages = ev.messages;
    if (ev.event.type === "close_session") {
      closeSessionReason = ev.event.data.reason;
    }
    yield ev;
  }
  await prisma.chatMessage.createMany({
    data: finalMessages.map(msg => ({
      chatId: args.chatId,
      message: { ...msg, content: msg.content.filter(isExpectedContentBlock) },
      userId: args.userId,
      ipAddress: args.ipAddress,
    })),
  });
  if (hv(closeSessionReason)) {
    await prisma.chat.update({
      where: { id: args.chatId },
      data: { closedReason: closeSessionReason },
    });
  }
}
