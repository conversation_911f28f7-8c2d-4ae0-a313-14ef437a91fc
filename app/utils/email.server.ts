import { ServerClient, Message } from "postmark";
import { envSecret } from "~/env.server";
import * as marked from "marked";
import { hv } from "~/Tools";
import * as fs from "node:fs/promises";
import * as path from "node:path";
import * as uuid from "uuid";

export const emailClient = new ServerClient(envSecret.POSTMARK_API_KEY);

const fromEmail = "<EMAIL>";
export const devEmail = "<EMAIL>";

function sanitizeMd(markdown: string) {
  return markdown
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;");
}

export async function sendEmail(
  emailClient: ServerClient,
  message: Omit<Message, "From"> & { From?: string },
) {
  if (hv(envSecret.DEBUG_EMAIL_DROPOFF_DIRECTORY)) {
    const filePath = path.join(
      envSecret.DEBUG_EMAIL_DROPOFF_DIRECTORY!,
      `${uuid.v7()}.json`,
    );
    await fs.writeFile(filePath, JSON.stringify(message, null, 2));
    return;
  }
  await emailClient.sendEmail({ From: fromEmail, ...message });
}

export async function sendMarkdownEmail(
  emailClient: ServerClient,
  message: Omit<Message, "From" | "HtmlBody" | "TextBody">,
  body: string,
) {
  const sanitizedBody = sanitizeMd(body);
  return await sendEmail(emailClient, {
    ...message,
    HtmlBody: await marked.parse(sanitizedBody),
  });
}

export async function sendExceptionEmail(
  emailClient: ServerClient,
  error: unknown,
  url: string,
) {
  const errorStr = error instanceof Error ? error.stack : `${error}`;
  return await sendEmail(emailClient, {
    To: devEmail,
    Subject: `Exception thrown at URL: ${url}`,
    TextBody: `${errorStr}`,
  });
}
