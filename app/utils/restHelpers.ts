import { data } from "react-router";
import { dbTypeFieldName } from "./dbExtensions.server";
import { SelectStructure } from "./selectFields";

const unsafeDbObjectMessage = "Unfiltered DB object, implement getClientFields";
const unsafeClassMessage =
  "Unfiltered class object, use selectFields or allow in restHelpers.ts";

type RemoveUnsafeObjectsResult<T> = T extends
  | Record<string | number | symbol, unknown>
  | unknown[]
  ? "getClientFields" extends keyof T
    ? T["getClientFields"] extends { structure: infer S }
      ? RemoveUnsafeObjectsResult<SelectStructure<T, S>>
      : never
    : typeof dbTypeFieldName extends keyof T
    ? typeof unsafeDbObjectMessage
    : { [K in keyof T]: RemoveUnsafeObjectsResult<T[K]> }
  : T extends Date
  ? T
  : T extends object
  ? typeof unsafeClassMessage
  : T;

function removeUnsafeObjects<TData>(
  obj: TData,
): RemoveUnsafeObjectsResult<TData> {
  if (typeof obj === "object" && obj !== null) {
    if (obj.constructor === Object || Array.isArray(obj)) {
      if ("getClientFields" in obj) {
        return removeUnsafeObjects((obj.getClientFields as () => TData)());
      } else if (dbTypeFieldName in obj) {
        return unsafeDbObjectMessage as RemoveUnsafeObjectsResult<TData>;
      } else {
        const result = (Array.isArray(obj) ? [] : {}) as Record<
          string | number,
          unknown
        >;
        for (const [k, v] of Object.entries(obj)) {
          result[k as string | number] = removeUnsafeObjects(v);
        }
        return result as RemoveUnsafeObjectsResult<TData>;
      }
      // only allow certain classes
    } else if (!(obj instanceof Date)) {
      return unsafeClassMessage as RemoveUnsafeObjectsResult<TData>;
    }
  }
  return obj as RemoveUnsafeObjectsResult<TData>;
}

export const safeData = <TData>(args: TData, init?: number | ResponseInit) => {
  return data(removeUnsafeObjects(args), init);
};
