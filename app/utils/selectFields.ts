type SelectFields<T, K> = {
  [P in K & keyof T]: T[P];
};

export function selectFields<
  T extends Record<string, unknown>,
  K extends keyof T,
>(obj: T, keys: readonly K[]): SelectFields<T, K> {
  const result = {} as SelectFields<T, K>;
  for (const key of keys) {
    if (key in obj) {
      result[key] = obj[key];
    }
  }
  return result;
}

export type Structure =
  | true
  | undefined
  | { [K in string | number | symbol]?: Structure };

type StructureOf<T> =
  | true
  | undefined
  | (T extends unknown[]
      ? { [K in keyof T & number]?: StructureOf<T[K]> }
      : T extends Record<string, unknown>
      ? { [K in keyof T]?: StructureOf<T[K]> }
      : never);

/** provides autocomplete for object members while allowing any additional structure */
export type ExtendsStructureOf<T> = StructureOf<T> | Structure;

export const selectAll = Symbol("selectAll");

export type SelectStructure<T, S> = S extends true
  ? T
  : S extends undefined
  ? undefined
  : T extends null | undefined
  ? T
  : S extends { [selectAll]: infer S }
  ? { [K in keyof T]: SelectStructure<T[K], S> }
  : T extends object
  ? { [K in keyof T & keyof S]: SelectStructure<T[K], S[K]> }
  : never;

export function selectStructure<T, S extends Structure>(
  obj: T,
  structure: S,
): SelectStructure<T, S> {
  if (structure === true) {
    return obj as SelectStructure<T, S>;
  }
  if (structure === undefined) {
    return undefined as SelectStructure<T, S>;
  }
  if (obj === null || obj === undefined) {
    return obj as SelectStructure<T, S>;
  }
  const result = (Array.isArray(obj) ? [] : {}) as Record<
    string | number,
    unknown
  >;
  if (selectAll in structure) {
    for (const [key, value] of Object.entries(obj)) {
      result[key as string | number] = selectStructure(
        value,
        structure[selectAll],
      );
    }
  } else if (typeof obj === "object") {
    for (const [key, value] of Object.entries(structure)) {
      if (key in obj) {
        result[key as string | number] = selectStructure(
          obj[key as keyof T],
          value as Structure,
        );
      }
    }
  }
  return result as SelectStructure<T, S>;
}
