import { createClerkClient } from "@clerk/react-router/api.server";
import { envSecret } from "~/env.server";
import { hv } from "~/Tools";

export const clerkClient = createClerkClient({
  secretKey: envSecret.CLERK_SECRET_KEY,
});

function* findClerkIds(
  obj: unknown,
  filter: (clerkId: string) => boolean,
): Generator<string> {
  if (Array.isArray(obj)) {
    for (const a of obj) {
      yield* findClerkIds(a, filter);
    }
  } else if (typeof obj === "object" && hv(obj)) {
    for (const v of Object.values(obj)) {
      yield* findClerkIds(v, filter);
    }
    if ("clerkId" in obj && filter(obj.clerkId as string)) {
      yield obj.clerkId as string;
    }
  }
}

export async function fetchClerkUserMap(obj: unknown) {
  const clerkIds = [
    ...new Set(findClerkIds(obj, clerkId => clerkId.startsWith("user"))),
  ];
  if (clerkIds.length > 0) {
    // empty array will fetch ALL users
    // note that this result is paginated, although that shouldn't be a problem for now
    const clerkUsers = await clerkClient.users.getUserList({
      userId: clerkIds,
    });
    return Object.fromEntries(
      clerkUsers.data.map(u => [
        u.id,
        {
          fullName: u.fullName,
          emailAddress: u.primaryEmailAddress?.emailAddress,
        },
      ]),
    );
  }
  return {};
}
