import { z, ZodString } from "zod";

// Keep an eye out on https://github.com/colinhacks/zod/issues/3928 for any RF opportunities here
// Zod eXtensions, we may attach these directly to the zod object z at some point
export const zx = {
  maybe(base: ZodString) {
    // Union may not play well with client side validation
    // If that is an issue, we can try to remove the min check from base._def.checks
    return z.union([
      base,
      z
        .literal("")
        // must be null, otherwise doesn't update the DB
        .transform(() => null)
        .nullable(),
    ]);
  },

  populatedArray<T extends z.ZodType>(z: T) {
    return z.array().nonempty();
  },

  defaultLiteral<T extends NonNullable<z.Primitive>>(value: T) {
    return z.literal(value).default(value);
  },

  percent: () => z.number().min(0).max(100),

  saneString: () => z.string().trim().max(10000).nonempty(),

  id: () => z.string().nonempty(), // just string for now
};
