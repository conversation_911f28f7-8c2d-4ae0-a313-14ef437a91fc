export function parseISODate(date: string) {
  const match = /(\d{4})-(\d{2})-(\d{2})/.exec(date);
  if (match === null) {
    throw new Error(`date ${date} must be in yyyy-MM-dd format`);
  }
  const [, year, month, day] = match;
  return new Date(Date.UTC(Number(year), Number(month) - 1, Number(day)));
}

export function calculateElapsedProrateRatio({
  firstDate,
  midDate,
  lastDate,
  significantDigits = 2,
}: {
  firstDate: string;
  midDate: string;
  lastDate: string;
  significantDigits?: number;
}) {
  const firstUnixTime = parseISODate(firstDate).getTime();
  const midUnixTime = parseISODate(midDate).getTime();
  const lastUnixTime = parseISODate(lastDate).getTime();
  if (!(firstUnixTime <= midUnixTime && midUnixTime <= lastUnixTime)) {
    throw new Error("mid date is not in range");
  }
  if (firstUnixTime === lastUnixTime) {
    return 1;
  }
  const ratio = (midUnixTime - firstUnixTime) / (lastUnixTime - firstUnixTime);
  return parseFloat(ratio.toFixed(significantDigits));
}

export function calculateAgeOnDate({
  birthDate,
  referenceDate,
}: {
  birthDate: string;
  referenceDate: string;
}) {
  const parsedBirthDate = parseISODate(birthDate);
  const parsedReferenceDate = parseISODate(referenceDate);

  let years = parsedReferenceDate.getFullYear() - parsedBirthDate.getFullYear();

  // Adjust if birthday hasn't occurred yet this year
  if (
    parsedReferenceDate.getMonth() < parsedBirthDate.getMonth() ||
    (parsedReferenceDate.getMonth() === parsedBirthDate.getMonth() &&
      parsedReferenceDate.getDate() < parsedBirthDate.getDate())
  ) {
    years--;
  }

  return years;
}

export function verifyIsBefore({
  pastDate,
  referenceDate,
  shouldIncludeReferenceDate,
  maxDaysInPast = undefined,
}: {
  pastDate: string;
  referenceDate: string;
  shouldIncludeReferenceDate: boolean;
  maxDaysInPast?: number;
}) {
  const parsedPastDate = parseISODate(pastDate);
  const maxDate = parseISODate(referenceDate);
  const isBeforeReferenceDate = shouldIncludeReferenceDate
    ? parsedPastDate <= maxDate
    : parsedPastDate < maxDate;
  if (maxDaysInPast === undefined) {
    return isBeforeReferenceDate;
  } else {
    const minDate = new Date(maxDate);
    minDate.setDate(maxDate.getDate() - maxDaysInPast);
    return isBeforeReferenceDate && parsedPastDate >= minDate;
  }
}

export function verifyIsAfter({
  futureDate,
  referenceDate,
  shouldIncludeReferenceDate,
  maxDaysInFuture = undefined,
}: {
  futureDate: string;
  referenceDate: string;
  shouldIncludeReferenceDate: boolean;
  maxDaysInFuture?: number;
}) {
  const parsedFutureDate = parseISODate(futureDate);
  const minDate = parseISODate(referenceDate);
  const isAfterReferenceDate = shouldIncludeReferenceDate
    ? parsedFutureDate >= minDate
    : parsedFutureDate > minDate;
  if (maxDaysInFuture === undefined) {
    return isAfterReferenceDate;
  } else {
    const maxDate = new Date(minDate);
    maxDate.setDate(minDate.getDate() + maxDaysInFuture);
    return isAfterReferenceDate && parsedFutureDate <= maxDate;
  }
}

export function addCalendarDays(isoDate: string, days: number): string {
  const date = parseISODate(isoDate);
  date.setDate(date.getDate() + days);
  return date.toISOString().slice(0, 10);
}

export function subtractCalendarDays(isoDate: string, days: number): string {
  return addCalendarDays(isoDate, -days);
}

export const ALL_LIBRARY_FUNCTIONS = [
  parseISODate,
  calculateElapsedProrateRatio,
  calculateAgeOnDate,
  verifyIsAfter,
  verifyIsBefore,
  addCalendarDays,
  subtractCalendarDays,
];
