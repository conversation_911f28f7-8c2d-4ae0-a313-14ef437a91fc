import { AnalysisLineItem } from "prisma/interfaces";
import { Assert } from "~/Tools";
import { sortBy } from "./Comparable";

export function annotateUnderwritingContent(
  lineItems: AnalysisLineItem[],
  underwritingContent: string,
) {
  const matches = [] as {
    match: string;
    index: number;
    lineItemId?: string;
  }[];

  const sortedTextSegments = lineItems
    .flatMap(item =>
      item.textSegments
        .map((text, i) => {
          if (text.includes("\n")) {
            Assert.soft(
              false,
              `text segment ${text} at ${item.id}.${i} contains newline`,
            );
            return null;
          }
          return {
            item,
            index: i,
            pattern: text,
          };
        })
        .filter(a => a !== null),
    )
    .toSorted(sortBy(t => t.pattern.length));

  const alreadyMatchedChar = "\x00";
  // usually there will still be some remaining unmatched characters
  // although typically it's just connecting whitespace
  // e.g. for rules "cars" and "airplanes"
  // the space in "cars airplanes" is not matched by any rule
  // and is also not in unmatched content
  const remainingCharsPattern = {
    item: null,
    index: 0,
    pattern: new RegExp(`[^${alreadyMatchedChar}]+`, "g"),
  };
  [...sortedTextSegments, remainingCharsPattern].reduce((acc, t) => {
    return acc.replace(t.pattern, (match, index) => {
      matches.push({ match, index, lineItemId: t.item?.id });
      // Do not spread the string, we want to preserve indexing, not graphemes
      // replace matched characters with NUL so they can't be matched again
      return alreadyMatchedChar.repeat(match.length);
    });
  }, underwritingContent);
  return matches.toSorted(sortBy(m => m.index));
}
