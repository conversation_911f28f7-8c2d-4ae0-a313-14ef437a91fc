// AKA Shortlex ordering
class RadixOrdering {
  constructor(public comparables: Comparable[]) {}
}

class Negative<T> {
  constructor(public inner: T) {}
}

type Comparable = number | string | Date | RadixOrdering | Negative<Comparable>;

export function inSequence(...comparables: Comparable[]) {
  return new RadixOrdering(comparables);
}

export function negative(comparable: Comparable) {
  if (typeof comparable === "number") {
    return -comparable;
  } else if (comparable instanceof Negative) {
    return comparable.inner;
  }
  return new Negative(comparable);
}

// Comparable can represent all possible orders (except mathematically esoteric ones we won't encounter),
// so this function is just "the" compare function
export function compare(left: Comparable, right: Comparable): number {
  if (left instanceof RadixOrdering) {
    if (!(right instanceof RadixOrdering)) {
      return 1;
    }
    const leftArray = left.comparables;
    const rightArray = right.comparables;
    if (leftArray.length < rightArray.length) {
      return -1;
    } else if (leftArray.length > rightArray.length) {
      return 1;
    } else {
      for (let i = 0; i < leftArray.length; i++) {
        const cmpResult = compare(leftArray[i]!, rightArray[i]!);
        if (cmpResult !== 0) {
          return cmpResult;
        }
      }
      return 0;
    }
  } else if (right instanceof RadixOrdering) {
    return -1;
  }
  if (left instanceof Negative) {
    if (!(right instanceof Negative)) {
      return -1;
    }
    return compare(right.inner, left.inner);
  } else if (right instanceof Negative) {
    return 1;
  }
  if (left < right) {
    return -1;
  } else if (left > right) {
    return 1;
  }
  return 0;
}

export function sortBy<T>(getter: (el: T) => Comparable) {
  return (el1: T, el2: T) => {
    return compare(getter(el1), getter(el2));
  };
}

export function sortByDesc<T>(getter: (el: T) => Comparable) {
  return (el1: T, el2: T) => {
    return compare(getter(el2), getter(el1));
  };
}
