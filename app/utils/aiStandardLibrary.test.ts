import { expect, test } from "vitest";
import {
  addCalendarDays,
  calculateAgeOnDate,
  calculateElapsedProrateRatio,
  parseISODate,
  subtractCalendarDays,
  verifyIsAfter,
  verifyIsBefore,
} from "./aiStandardLibrary";

test("parseISODate", () => {
  expect(parseISODate("2016-02-29").toISOString().slice(0, 10), "2016-02-29");
  expect(parseISODate("2025-03-07").toISOString().slice(0, 10), "2025-03-07");
  expect(() => parseISODate("2025-1-1").toISOString().slice(0, 10)).toThrow();
});

test("calculateElapsedProrateRatio", () => {
  expect(() =>
    calculateElapsedProrateRatio({
      firstDate: "2025-01-15",
      midDate: "2025-01-14",
      lastDate: "2025-01-13",
    }),
  ).toThrow();
  expect(
    calculateElapsedProrateRatio({
      firstDate: "2025-01-15",
      midDate: "2025-01-15",
      lastDate: "2025-01-15",
    }),
  ).toEqual(1);
  expect(
    calculateElapsedProrateRatio({
      firstDate: "2025-01-15",
      midDate: "2025-01-15",
      lastDate: "2025-02-15",
    }),
  ).toEqual(0);
  expect(
    calculateElapsedProrateRatio({
      firstDate: "2025-01-15",
      midDate: "2025-02-15",
      lastDate: "2025-02-15",
    }),
  ).toEqual(1);
  expect(
    calculateElapsedProrateRatio({
      firstDate: "2025-04-01",
      midDate: "2025-04-02",
      lastDate: "2025-04-04",
    }),
  ).toEqual(0.33);
  expect(
    calculateElapsedProrateRatio({
      firstDate: "2025-01-01",
      midDate: "2025-03-01",
      lastDate: "2026-01-01",
      significantDigits: 100,
    }),
  ).toEqual((31 + 28) / 365);
  expect(
    calculateElapsedProrateRatio({
      firstDate: "2024-03-01",
      midDate: "2024-05-01",
      lastDate: "2025-03-01",
      significantDigits: 100,
    }),
  ).toEqual((31 + 30) / 365);
  expect(
    calculateElapsedProrateRatio({
      firstDate: "2023-12-01",
      midDate: "2024-03-01",
      lastDate: "2024-12-01",
      significantDigits: 100,
    }),
  ).toEqual((31 + 31 + 29) / 366);
});

test("calculateAgeOnDate", () => {
  expect(
    calculateAgeOnDate({
      birthDate: "2004-02-29",
      referenceDate: "2025-03-01",
    }),
  ).toEqual(21);
  expect(
    calculateAgeOnDate({
      birthDate: "2004-02-29",
      referenceDate: "2025-02-28",
    }),
  ).toEqual(20);
  expect(
    calculateAgeOnDate({
      birthDate: "2004-02-29",
      referenceDate: "2024-02-29",
    }),
  ).toEqual(20);
});

test("verifyIsBefore", () => {
  expect(
    verifyIsBefore({
      pastDate: "1999-12-31",
      referenceDate: "2000-01-01",
      shouldIncludeReferenceDate: false,
    }),
  ).toEqual(true);
  expect(
    verifyIsBefore({
      pastDate: "1999-12-31",
      referenceDate: "1999-12-31",
      shouldIncludeReferenceDate: false,
    }),
  ).toEqual(false);
  expect(
    verifyIsBefore({
      pastDate: "1999-12-31",
      referenceDate: "1999-12-31",
      shouldIncludeReferenceDate: true,
    }),
  ).toEqual(true);
  expect(
    verifyIsBefore({
      pastDate: "1999-01-01",
      referenceDate: "2000-01-01",
      shouldIncludeReferenceDate: true,
      maxDaysInPast: 365,
    }),
  ).toEqual(true);
  expect(
    verifyIsBefore({
      pastDate: "1998-12-31",
      referenceDate: "2000-01-01",
      shouldIncludeReferenceDate: true,
      maxDaysInPast: 365,
    }),
  ).toEqual(false);
});

test("verifyIsAfter", () => {
  expect(
    verifyIsAfter({
      futureDate: "2000-01-01",
      referenceDate: "1999-12-31",
      shouldIncludeReferenceDate: false,
    }),
  ).toEqual(true);
  expect(
    verifyIsAfter({
      futureDate: "1999-12-31",
      referenceDate: "1999-12-31",
      shouldIncludeReferenceDate: false,
    }),
  ).toEqual(false);
  expect(
    verifyIsAfter({
      futureDate: "1999-12-31",
      referenceDate: "1999-12-31",
      shouldIncludeReferenceDate: true,
    }),
  ).toEqual(true);
  expect(
    verifyIsAfter({
      futureDate: "2000-01-01",
      referenceDate: "1999-01-01",
      shouldIncludeReferenceDate: true,
      maxDaysInFuture: 365,
    }),
  ).toEqual(true);
  expect(
    verifyIsAfter({
      futureDate: "2000-01-01",
      referenceDate: "1998-12-31",
      shouldIncludeReferenceDate: true,
      maxDaysInFuture: 365,
    }),
  ).toEqual(false);
});

test("addCalendarDays", () => {
  expect(addCalendarDays("2025-01-15", 1)).toEqual("2025-01-16");
  expect(addCalendarDays("2025-01-31", 1)).toEqual("2025-02-01");
  expect(addCalendarDays("2025-01-01", 31)).toEqual("2025-02-01");
  expect(addCalendarDays("2024-02-28", 1)).toEqual("2024-02-29");
  expect(addCalendarDays("2024-02-29", 1)).toEqual("2024-03-01");
  expect(addCalendarDays("2023-02-28", 1)).toEqual("2023-03-01");
  expect(addCalendarDays("2024-12-31", 1)).toEqual("2025-01-01");
  expect(addCalendarDays("2024-12-31", 366)).toEqual("2026-01-01");
  expect(addCalendarDays("2025-04-03", 30)).toEqual("2025-05-03");
});

test("subtractCalendarDays", () => {
  expect(subtractCalendarDays("2025-01-15", 1)).toEqual("2025-01-14");
  expect(subtractCalendarDays("2025-02-01", 1)).toEqual("2025-01-31");
  expect(subtractCalendarDays("2025-02-01", 31)).toEqual("2025-01-01");
  expect(subtractCalendarDays("2024-03-01", 1)).toEqual("2024-02-29");
  expect(subtractCalendarDays("2024-02-29", 1)).toEqual("2024-02-28");
  expect(subtractCalendarDays("2023-03-01", 1)).toEqual("2023-02-28");
  expect(subtractCalendarDays("2025-01-01", 1)).toEqual("2024-12-31");
  expect(subtractCalendarDays("2025-01-01", 366)).toEqual("2024-01-01");
});
