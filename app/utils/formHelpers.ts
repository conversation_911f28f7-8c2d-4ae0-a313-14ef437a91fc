import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import {
  Control,
  FieldPath,
  FieldValues,
  Path,
  useController,
  useForm,
  UseFormProps,
  UseFormReturn,
} from "react-hook-form";
import { z, ZodSchema } from "zod";
import { hv } from "~/Tools";

// Full type inference
export const useZodForm = <T extends ZodSchema>(
  schema: T,
  options?: UseFormProps<z.infer<T>>,
) => {
  return useForm<z.infer<T>>({
    resolver: zodResolver(schema),
    ...options,
  });
};

export function useField<
  TFieldValues extends FieldValues,
  TName extends FieldPath<TFieldValues>,
>(control: Control<TFieldValues>, name: TName) {
  const {
    field: { ref, ...field },
  } = useController({ control, name });
  return field;
}

export function setAndReportNestedError<V extends FieldValues>(
  form: UseFormReturn<V>,
  error: z.ZodFormattedError<unknown>,
  name = "",
) {
  const { _errors, ...nestedError } = error;
  if (_errors.length > 0) {
    form.setError(name as Path<V>, { message: _errors[0] });
    const field = form.getFieldState(name as Path<V>);
    const { ref, message } = field.error!;
    if (hv(ref) && "setCustomValidity" in ref && hv(message)) {
      ref.setCustomValidity(message);
      ref.reportValidity();
    }
  }
  for (const [k, v] of Object.entries(nestedError)) {
    setAndReportNestedError(
      form,
      v as unknown as z.ZodFormattedError<unknown>,
      name !== "" ? `${name}.${k}` : k,
    );
  }
}

export function useReportServerError<V extends FieldValues>(
  form: UseFormReturn<V>,
  errors: z.ZodFormattedError<unknown> | undefined,
) {
  useEffect(() => {
    if (hv(errors)) {
      setAndReportNestedError(form, errors);
    }
    // we only care about changes in error here
    // this use case will be resolved by useEffectEvent in a future react version
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [errors]);
}
