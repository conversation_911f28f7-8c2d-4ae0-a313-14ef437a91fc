import { Tool } from "@anthropic-ai/sdk/resources/messages.mjs";
import {
  anthropicClient,
  CLAUDE_LATEST_MODEL,
  sonnetMaxOutputTokens,
} from "~/llm/anthropic.server";
import {
  Assert,
  getSchemaErrors,
  hv,
  logify,
  no,
  pretty,
  Union,
} from "~/Tools";
import {
  appSchemaConversionPromptMd,
  reportDataSchemaMismatchTool,
  RequestCorrectionsInput,
} from "~/utils/appSchemaConversionPrompt";

export async function convertNatLangApplicationToJson<T = unknown>(
  applicationMd: string,
  jsonSchema: Tool.InputSchema,
): Promise<
  Union<{
    applicationJson: T;
    errorsMd: string;
  }>
> {
  const submitSchemaJsonTool: Tool = {
    name: "submit_schema_json",
    input_schema: jsonSchema,
  };
  console.log("=== Using AI to convert natural language to JSON...");
  const response = await anthropicClient.messages.create({
    max_tokens: sonnetMaxOutputTokens,
    temperature: 0,
    model: CLAUDE_LATEST_MODEL,
    tools: [reportDataSchemaMismatchTool, submitSchemaJsonTool],
    tool_choice: { type: "any" },
    system: [
      {
        text: appSchemaConversionPromptMd,
        type: "text",
        cache_control: { type: "ephemeral" },
      },
    ],
    messages: [{ role: "user", content: applicationMd }],
  });

  logify(response);

  const firstContent = response.content[0] ?? no("No content returned!");
  Assert.soft(response.content.length === 1, "More than 1 content returned!");

  Assert.hard(firstContent.type === "tool_use", "Expected tool use");

  if (firstContent.name === reportDataSchemaMismatchTool.name) {
    const input = firstContent.input as RequestCorrectionsInput;
    return { errorsMd: input.requestMd };
  } else if (firstContent.name === submitSchemaJsonTool.name) {
    console.log("=== Running deterministic JSON validator");
    const schemaErrors = getSchemaErrors(firstContent.input, jsonSchema);
    console.log("HAS ERRORS?");
    console.log(hv(schemaErrors));
    console.log("Errors:");
    logify(schemaErrors);

    if (hv(schemaErrors)) {
      return { errorsMd: pretty(schemaErrors) };
    }

    return { applicationJson: firstContent.input as T };
  }

  throw new Error(`Unexpected content: ${pretty(firstContent)}`);
}
