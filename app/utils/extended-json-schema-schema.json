{"type": "object", "properties": {"schema": {"$id": "extended-schema", "type": "object", "properties": {"format": {"type": "string", "enum": ["date", "time", "date-time", "iso-time", "iso-date-time", "duration", "uri", "int32", "int64", "float", "double"]}, "x-validate-fn": {"anyOf": [{"type": "object", "properties": {"code": {"type": "string"}, "parameters": {"type": "array", "items": {"type": "string"}}, "errorMessage": {"type": "string"}, "description": {"type": "string"}}, "required": ["code", "parameters", "errorMessage"], "additionalProperties": false}, {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "parameters": {"type": "array", "items": {"type": "string"}}, "errorMessage": {"type": "string"}, "description": {"type": "string"}}, "required": ["code", "parameters", "errorMessage"], "additionalProperties": false}, "minItems": 1}]}, "x-output-fn": {"type": "object", "properties": {"code": {"type": "string"}, "parameters": {"type": "array", "items": {"type": "string"}}, "description": {"type": "string"}}, "required": ["code", "parameters"], "additionalProperties": false}, "x-text-analysis": {"type": "object", "properties": {"validationDirections": {"type": "string"}, "description": {"type": "string"}}, "required": ["validationDirections"], "additionalProperties": false}}, "$schema": "https://json-schema.org/draft/2020-12/schema", "$vocabulary": {"https://json-schema.org/draft/2020-12/vocab/core": true, "https://json-schema.org/draft/2020-12/vocab/applicator": true, "https://json-schema.org/draft/2020-12/vocab/unevaluated": true, "https://json-schema.org/draft/2020-12/vocab/validation": true, "https://json-schema.org/draft/2020-12/vocab/meta-data": true, "https://json-schema.org/draft/2020-12/vocab/format-annotation": true, "https://json-schema.org/draft/2020-12/vocab/content": true}, "$dynamicAnchor": "meta", "title": "Core and Validation specifications meta-schema", "allOf": [{"$schema": "https://json-schema.org/draft/2020-12/schema", "title": "Core vocabulary meta-schema", "type": ["object", "boolean"], "properties": {"$id": {"$comment": "Non-empty fragments not allowed.", "pattern": "^[^#]*#?$", "type": "string", "format": "uri-reference"}, "$schema": {"type": "string", "format": "uri"}, "$ref": {"type": "string", "format": "uri-reference"}, "$anchor": {"type": "string", "pattern": "^[A-Za-z_][-A-Za-z0-9._]*$"}, "$dynamicRef": {"type": "string", "format": "uri-reference"}, "$dynamicAnchor": {"type": "string", "pattern": "^[A-Za-z_][-A-Za-z0-9._]*$"}, "$vocabulary": {"type": "object", "propertyNames": {"type": "string", "format": "uri"}, "additionalProperties": {"type": "boolean"}}, "$comment": {"type": "string"}, "$defs": {"type": "object", "additionalProperties": {"$dynamicRef": "#meta"}}}, "$defs": {"anchorString": {"type": "string", "pattern": "^[A-Za-z_][-A-Za-z0-9._]*$"}, "uriString": {"type": "string", "format": "uri"}, "uriReferenceString": {"type": "string", "format": "uri-reference"}}}, {"$schema": "https://json-schema.org/draft/2020-12/schema", "title": "Applicator vocabulary meta-schema", "type": ["object", "boolean"], "properties": {"prefixItems": {"type": "array", "minItems": 1, "items": {"$dynamicRef": "#meta"}}, "items": {"$dynamicRef": "#meta"}, "contains": {"$dynamicRef": "#meta"}, "additionalProperties": {"$dynamicRef": "#meta"}, "properties": {"type": "object", "additionalProperties": {"$dynamicRef": "#meta"}, "default": {}}, "patternProperties": {"type": "object", "additionalProperties": {"$dynamicRef": "#meta"}, "propertyNames": {"format": "regex"}, "default": {}}, "dependentSchemas": {"type": "object", "additionalProperties": {"$dynamicRef": "#meta"}, "default": {}}, "propertyNames": {"$dynamicRef": "#meta"}, "if": {"$dynamicRef": "#meta"}, "then": {"$dynamicRef": "#meta"}, "else": {"$dynamicRef": "#meta"}, "allOf": {"type": "array", "minItems": 1, "items": {"$dynamicRef": "#meta"}}, "anyOf": {"type": "array", "minItems": 1, "items": {"$dynamicRef": "#meta"}}, "oneOf": {"type": "array", "minItems": 1, "items": {"$dynamicRef": "#meta"}}, "not": {"$dynamicRef": "#meta"}}, "$defs": {"schemaArray": {"type": "array", "minItems": 1, "items": {"$dynamicRef": "#meta"}}}}, {"$schema": "https://json-schema.org/draft/2020-12/schema", "title": "Unevaluated applicator vocabulary meta-schema", "type": ["object", "boolean"], "properties": {"unevaluatedItems": {"$dynamicRef": "#meta"}, "unevaluatedProperties": {"$dynamicRef": "#meta"}}}, {"$schema": "https://json-schema.org/draft/2020-12/schema", "title": "Validation vocabulary meta-schema", "type": ["object", "boolean"], "properties": {"type": {"anyOf": [{"enum": ["array", "boolean", "integer", "null", "number", "object", "string"]}, {"type": "array", "items": {"enum": ["array", "boolean", "integer", "null", "number", "object", "string"]}, "minItems": 1, "uniqueItems": true}]}, "const": true, "enum": {"type": "array", "items": true}, "multipleOf": {"type": "number", "exclusiveMinimum": 0}, "maximum": {"type": "number"}, "exclusiveMaximum": {"type": "number"}, "minimum": {"type": "number"}, "exclusiveMinimum": {"type": "number"}, "maxLength": {"type": "integer", "minimum": 0}, "minLength": {"default": 0, "type": "integer", "minimum": 0}, "pattern": {"type": "string", "format": "regex"}, "maxItems": {"type": "integer", "minimum": 0}, "minItems": {"default": 0, "type": "integer", "minimum": 0}, "uniqueItems": {"type": "boolean", "default": false}, "maxContains": {"type": "integer", "minimum": 0}, "minContains": {"type": "integer", "minimum": 0, "default": 1}, "maxProperties": {"type": "integer", "minimum": 0}, "minProperties": {"default": 0, "type": "integer", "minimum": 0}, "required": {"type": "array", "items": {"type": "string"}, "uniqueItems": true, "default": []}, "dependentRequired": {"type": "object", "additionalProperties": {"type": "array", "items": {"type": "string"}, "uniqueItems": true, "default": []}}}, "$defs": {"nonNegativeInteger": {"type": "integer", "minimum": 0}, "nonNegativeIntegerDefault0": {"type": "integer", "minimum": 0, "default": 0}, "simpleTypes": {"enum": ["array", "boolean", "integer", "null", "number", "object", "string"]}, "stringArray": {"type": "array", "items": {"type": "string"}, "uniqueItems": true, "default": []}}}, {"$schema": "https://json-schema.org/draft/2020-12/schema", "title": "Meta-data vocabulary meta-schema", "type": ["object", "boolean"], "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "default": true, "deprecated": {"type": "boolean", "default": false}, "readOnly": {"type": "boolean", "default": false}, "writeOnly": {"type": "boolean", "default": false}, "examples": {"type": "array", "items": true}}}, {"$schema": "https://json-schema.org/draft/2020-12/schema", "title": "Format vocabulary meta-schema for annotation results", "type": ["object", "boolean"], "properties": {"format": {"type": "string"}}}, {"$schema": "https://json-schema.org/draft/2020-12/schema", "title": "Content vocabulary meta-schema", "type": ["object", "boolean"], "properties": {"contentEncoding": {"type": "string"}, "contentMediaType": {"type": "string"}, "contentSchema": {"$dynamicRef": "#meta"}}}], "$comment": "This meta-schema also defines keywords that have appeared in previous drafts in order to prevent incompatible extensions as they remain in common use."}}, "required": ["schema"]}