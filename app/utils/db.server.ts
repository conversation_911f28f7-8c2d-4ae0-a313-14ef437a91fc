import { Prisma, PrismaClient } from "@prisma/client";
import { PrismaClientKnownRequestError } from "@prisma/client/runtime/library";
import { customAlphabet } from "nanoid";
import { withNestedOperations } from "prisma-extension-nested-operations";
import { Assert, hasValueFn, hv } from "~/Tools";
import { envSecret } from "~/env.server";
import { modelExtension, resultExtension } from "./dbExtensions.server";

// NOTE: need to restart vite after changing this function, since it doesn't refresh the existing prisma client
const createPrismaClient = () => {
  const rawPrisma = new PrismaClient();

  type ModelName = keyof Prisma.TypeMap["model"];
  type Operations = Prisma.TypeMap["model"][ModelName]["operations"];
  const modelsWithDeletedAt = Object.fromEntries(
    Prisma.dmmf.datamodel.models
      .filter(m => m.fields.some(f => f.name == "deletedAt"))
      .map(m => [m.name, null] as [ModelName, null]),
  );

  const modelGenId = Object.fromEntries(
    Prisma.dmmf.datamodel.models
      .map(m => {
        const idField = m.fields.find(
          f =>
            f.name === "id" &&
            f.default === "" &&
            f.nativeType?.[0] === "VarChar",
        );
        return hasValueFn(
          idField?.nativeType?.[1][0],
          lengthStr => [m.name, () => genNanoid(Number(lengthStr))] as const,
        );
      })
      .filter(hv),
  );

  const opsWithWhere = {
    findFirst: null,
    findFirstOrThrow: null,
    findMany: null,
    updateMany: null,
    upsert: null,
    aggregate: null,
    groupBy: null,
    count: null,
  } satisfies {
    [K in keyof Operations]?: null; // could use key remapping here to make sure we have operations that have WhereInput
  };

  const retryOnSetIdFail = async <T>(f: () => Promise<T>, retryCount = 10) => {
    for (let i = 0; ; i++) {
      try {
        return await f();
      } catch (e) {
        if (e instanceof PrismaClientKnownRequestError) {
          const meta = e.meta as { target?: string[] } | undefined;
          if (meta?.target?.includes("id") === true) {
            if (i < retryCount) {
              continue;
            } else {
              Assert.soft(
                false,
                `retry failed after ${retryCount} attempts, see thrown error for details`,
              );
            }
          }
        }
        throw e;
      }
    }
  };
  const prisma = rawPrisma
    .$extends({
      name: "default nanoid",
      query: {
        $allModels: {
          $allOperations: withNestedOperations({
            $rootOperation({ model, operation, args, query }) {
              const genId = modelGenId[model];
              if (!hv(genId)) {
                return query(args);
              }
              if (operation === "create") {
                const data = args.data;
                return retryOnSetIdFail(() => {
                  args.data = {
                    id: genId(),
                    ...data,
                  } as typeof data;
                  return query(args);
                });
              } else if (
                operation === "createMany" ||
                operation === "createManyAndReturn"
              ) {
                const data = args.data;
                return retryOnSetIdFail(() => {
                  if (data instanceof Array) {
                    args.data = data.map(d => ({
                      id: genId(),
                      ...d,
                    })) as typeof data;
                  } else {
                    args.data = {
                      id: genId(),
                      ...data,
                    } as typeof data;
                  }
                  return query(args);
                });
              } else if (operation === "upsert") {
                if (!(model in modelGenId)) {
                  return query(args);
                }
                const data = args.create;
                return retryOnSetIdFail(() => {
                  args.create = {
                    id: genId(),
                    ...data,
                  } as typeof data;
                  return query(args);
                });
              }
              return query(args);
            },
            $allNestedOperations({ model, operation, args, query }) {
              const genId = modelGenId[model];
              if (!hv(genId)) {
                return query(args);
              }
              if (operation === "create") {
                const data = args;
                return retryOnSetIdFail(() => {
                  args = { id: genId(), ...data };
                  return query(args);
                });
              } else if (operation === "createMany") {
                const data = args.data;
                return retryOnSetIdFail(() => {
                  if (data instanceof Array) {
                    args.data = data.map(d => ({ id: genId(), ...d }));
                  } else {
                    args.data = { id: genId(), ...data };
                  }
                  return query(args);
                });
              } else if (
                operation === "upsert" ||
                operation === "connectOrCreate"
              ) {
                if (!(model in modelGenId)) {
                  return query(args);
                }
                const data = args.create;
                return retryOnSetIdFail(() => {
                  args.create = { id: genId(), ...data };
                  return query(args);
                });
              }
              return query(args);
            },
          }),
        },
      },
    })
    .$extends({
      name: "filter out deletedAt",
      query: {
        $allModels: {
          $allOperations: withNestedOperations({
            $rootOperation({ model, operation, args, query }) {
              // could be made more efficient by using the model and ops as keys instead of $all
              if (!(model in modelsWithDeletedAt)) {
                return query(args);
              }
              if (operation in opsWithWhere) {
                type WhereArgs = { where: { deletedAt?: unknown } };
                (args as WhereArgs).where = {
                  deletedAt: null,
                  ...(args as WhereArgs).where,
                };
              }
              return query(args);
            },
            $allNestedOperations({ model, operation, args, query }) {
              if (!(model in modelsWithDeletedAt)) {
                return query(args);
              }
              if (operation === "where") {
                return query({ deletedAt: null, ...args });
              }
              if (operation === "include" && typeof args === "object") {
                type WhereArgs = { where: { deletedAt?: unknown } };
                (args as WhereArgs).where = {
                  deletedAt: null,
                  ...(args as WhereArgs).where,
                };
              }
              return query(args);
            },
          }),
        },
      },
    })
    .$extends(modelExtension)
    .$extends(resultExtension);
  return prisma;
};

export type ExtendedPrismaClient = ReturnType<typeof createPrismaClient>;

let prisma: ReturnType<typeof createPrismaClient>;

if (envSecret.NODE_ENV === "production") {
  prisma = createPrismaClient();
} else {
  global.__db_hidden ??= createPrismaClient();
  prisma = global.__db_hidden;
}

// Claude recs for
// For 1-10K items: use 6 chars
// For 10K-100K items: use 7 chars
// For 100K-1M items: use 8 chars
// For 1M-10M items: use 9 chars
// For 10M-100M items: use 10 chars
// For 100M-1B items: use 11 chars

export function genNanoid(length: number) {
  const alphabet = "abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ23456789";
  return customAlphabet(alphabet)(length);
}

export { prisma };
