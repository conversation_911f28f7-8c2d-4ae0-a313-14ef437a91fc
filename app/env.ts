import { z } from "zod";

const envSchema = z.object({
  VITE_CLERK_PUBLISHABLE_KEY: z.string(),
  VITE_DEBUG_CLEAR_ACTIVE_ORG_ON_CLICK_ORG: z.coerce.boolean().default(false),
  VITE_POSTHOG_PUBLIC_KEY: z.string(),
  VITE_POSTHOG_PUBLIC_HOST: z.string(),
});
export const envPublic = envSchema.parse({
  // empty in prod, which has to use process.env
  ...import.meta.env,
  // however, process is not defined as a variable in dev
  ...(typeof process !== "undefined" ? process.env : null),
});
