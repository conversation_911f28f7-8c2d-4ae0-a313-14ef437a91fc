import {
  OrganizationList,
  useOrganization,
  useOrganizationList,
  UserButton,
} from "@clerk/react-router";
import { Flex, TabNav } from "@radix-ui/themes";
import { Link, useLocation } from "react-router";
import { envPublic } from "~/env";
import { CURRENT_PRODUCER_CONFIG, ProducerConfig } from "~/producers";
import { hv } from "~/Tools";

export function LoggedInButton(props: { config: ProducerConfig }) {
  const orgList = useOrganizationList({
    userMemberships: true,
  });
  const { organization } = useOrganization();
  if (!hv(organization)) {
    // happens instantaneously when redirecting for the no organization case
    // asserting causes the app to crash during hydration
    return <></>;
  }
  return (
    <UserButton appearance={{ baseTheme: props.config.clerkTheme }}>
      <UserButton.MenuItems>
        <UserButton.Action
          label={organization.name}
          labelIcon={<img src={organization.imageUrl} alt="Logo" />}
          onClick={() => {
            if (envPublic.VITE_DEBUG_CLEAR_ACTIVE_ORG_ON_CLICK_ORG) {
              if (orgList.isLoaded) {
                void orgList.setActive({ organization: null });
              }
            }
          }}
        />
      </UserButton.MenuItems>
    </UserButton>
  );
}

export function LoggedInNav({ producer }: { producer: ProducerConfig }) {
  const { pathname } = useLocation();
  return (
    <>
      <Flex gap="3" mb="3" justify="between" asChild>
        <header>
          <Link to="/rules">
            <img
              alt="logo"
              src={`/${producer.logoFilename}`}
              style={{ maxHeight: "30px", width: "auto" }}
            />
          </Link>
          <div style={{ alignSelf: "flex-end" }}>
            <LoggedInButton config={producer} />
          </div>
        </header>
      </Flex>
      <TabNav.Root>
        <TabNav.Link asChild active={pathname.startsWith("/home")}>
          <Link to="/home">Home</Link>
        </TabNav.Link>
        <TabNav.Link
          asChild
          active={pathname.startsWith("/underwritingPrograms")}
        >
          <Link to="/underwritingPrograms">Programs</Link>
        </TabNav.Link>
        <TabNav.Link asChild active={pathname.startsWith("/history")}>
          <Link to="/history">History</Link>
        </TabNav.Link>
        <TabNav.Link asChild active={pathname.startsWith("/ai-chat-debug")}>
          <Link to="/ai-chat-debug">{producer.aiName}</Link>
        </TabNav.Link>
      </TabNav.Root>
    </>
  );
}

export function LoggedInWrapper(props: { children: React.ReactNode }) {
  const { organization } = useOrganization();
  if (!hv(organization)) {
    // happens instantaneously when redirecting for the no organization case
    // asserting causes the app to crash during hydration
    return (
      <Flex px="5" py="7" justify="center">
        <OrganizationList hidePersonal />
      </Flex>
    );
  }
  return (
    <Flex
      direction="column"
      width="min(1024px, 100%)"
      mx="auto"
      p="5"
      flexGrow="1"
      asChild
    >
      <article>
        <LoggedInNav producer={CURRENT_PRODUCER_CONFIG} />
        {props.children}
      </article>
    </Flex>
  );
}
