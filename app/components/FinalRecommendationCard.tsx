import { RuleCategory } from "prisma/interfaces";
import {
  Badge,
  BadgeProps,
  Box,
  Button,
  Card,
  DataList,
  Dialog,
  Flex,
  Text,
  Tooltip,
} from "@radix-ui/themes";
import { useToggle } from "@uidotdev/usehooks";
import { format } from "date-fns";
import { ChevronDown, ChevronUp } from "lucide-react";
import React, { useState } from "react";
import { BadgeColor, CategoryBadgeColor, RuleUiStrings } from "~/Constants";
import {
  ClientAnalysisResult,
  ClientVersionedRule,
} from "~/lib/analysisHelpers";
import { riskThresholdPct } from "~/llm/config";
import { RecommendedAction } from "~/llm/mainPrompt.server";
import { RuleCard } from "~/routes/underwritingPrograms.$id";
import { hasValueFn, hv, no, softNo } from "~/Tools";
import { fetchClerkUserMap } from "~/utils/clerkHelpers.server";
import { inSequence, sortBy } from "~/utils/Comparable";

export const recommendedActionUiInfo: Record<
  RecommendedAction,
  { color: BadgeProps["color"]; uiName: string }
> = {
  CONTENT_EVALUATION: { color: "amber", uiName: "Content Evaluation Required" },
  DECLINE: { color: "red", uiName: "Decline Coverage" },
  APPROVE: { color: "green", uiName: "Approve Coverage" },
  OTHER_EVALUATION: { color: "amber", uiName: "Other Evaluation Required" },
  RAISE_PREMIUM: { color: "blue", uiName: "Raise Premium and Approve" },
  RISK_EVALUATION: { color: "amber", uiName: "Risk Evaluation Required" },
  RULE_EVALUATION: { color: "amber", uiName: "Rule Evaluation Required" },
};

// This order can be changed - used in UI.  Numbers should be in order
const AnalysisResultRuleSortMap: Record<RuleCategory, number> = {
  EXCLUDE: 0,
  REQUIRES_UNDERWRITER_REVIEW: 1,
  FLAT_PREMIUM: 3,
  PERCENT_PREMIUM: 4,
  INCLUDE: 5,
};

function joinQuotedText(textStrings: string[]) {
  return textStrings.map(s => `"${s}"`).join(" ");
}

function ShowRuleButton(props: { rule: ClientVersionedRule }) {
  const [dialogOpen, setDialogOpen] = useState(false);
  return (
    <Dialog.Root open={dialogOpen} onOpenChange={setDialogOpen}>
      <Dialog.Trigger>
        <Button size="1" color="gray" variant="surface" style={{ width: 100 }}>
          Show Rule
        </Button>
      </Dialog.Trigger>
      <Dialog.Content>
        <Dialog.Title>Submitted Rule</Dialog.Title>
        <RuleCard {...props} isReadonly />
        <Flex justify="center" mt="3">
          <Button onClick={() => setDialogOpen(false)}>Close</Button>
        </Flex>
      </Dialog.Content>
    </Dialog.Root>
  );
}

function SubmittedRulesButton(props: { rules: ClientVersionedRule[] }) {
  const [dialogOpen, setDialogOpen] = useState(false);
  return (
    <Dialog.Root open={dialogOpen} onOpenChange={setDialogOpen}>
      <Dialog.Trigger>
        <Button size="1" color="gray" variant="surface" mt="1">
          Show Submitted Rules
        </Button>
      </Dialog.Trigger>
      <Dialog.Content>
        <Dialog.Title>Submitted Rules</Dialog.Title>
        <Flex direction="column" gap="3">
          {props.rules.map(rule => (
            <RuleCard key={rule.id} rule={rule} isReadonly />
          ))}
        </Flex>
        <Flex justify="center" mt="3">
          <Button onClick={() => setDialogOpen(false)}>Close</Button>
        </Flex>
      </Dialog.Content>
    </Dialog.Root>
  );
}

type ClientLineItem = ClientAnalysisResult["lineItems"][0] & {
  versionedRule: ClientVersionedRule | null;
};

function getLineItemColor(item: ClientLineItem) {
  switch (item.contentType) {
    case "ADVERSARIAL":
      return "red";
    case "CONNECTIVE":
      return "cyan";
    case "INSCRUTABLE":
      return "yellow";
    case "UNMATCHED_RISK":
      if ((item.riskPct ?? no("No risk percent")) >= riskThresholdPct) {
        return "orange";
      } else {
        return "lime";
      }
    case "RULE_MATCH": {
      return CategoryBadgeColor[item.versionedRule!.category];
    }
  }
}

function AnnotatedUnderwritingContent({
  result,
}: {
  result: Omit<ClientAnalysisResult, "lineItems"> & {
    lineItems: ClientLineItem[];
  };
}) {
  return (
    <span style={{ whiteSpace: "pre-wrap" }}>
      {result.annotatedUnderwritingContent.map(m => {
        if (!hv(m.lineItemId)) {
          return <React.Fragment key={m.index}>{m.match}</React.Fragment>;
        }
        const lineItem =
          result.lineItems.find(l => l.id === m.lineItemId) ?? no();
        return (
          <Box
            key={m.index}
            display="inline-block"
            data-accent-color={getLineItemColor(lineItem)}
            style={{
              backgroundColor: "var(--accent-a4)",
            }}
          >
            {m.match}
          </Box>
        );
      })}
    </span>
  );
}

export function FinalRecommendationCard({
  result,
  unfilteredVersionedRules, // for ALL results
  clerkUserMap,
}: {
  result: ClientAnalysisResult;
  unfilteredVersionedRules: ClientVersionedRule[];
  clerkUserMap: Awaited<ReturnType<typeof fetchClerkUserMap>>;
}) {
  const versionedRules = result.versionedRules.map(
    ({ id }) => unfilteredVersionedRules.find(r => r.id === id) ?? no(),
  );
  const uiInfo = recommendedActionUiInfo[result.recommendedAction];
  const [isShowingDetail, toggleDetail] = useToggle(false);

  const lineItems = result.lineItems.map(item => ({
    ...item,
    versionedRule: hasValueFn(
      item.versionedRuleId,
      id =>
        versionedRules.find(r => r.id === id) ??
        no(`Rule not found: ${item.versionedRuleId}`),
    ),
  }));

  const adversarialItems = lineItems.filter(
    item => item.contentType === "ADVERSARIAL",
  );
  const adversarialItem = adversarialItems[0] ?? null;

  const unmatchedItems = lineItems.filter(
    item => item.contentType === "UNMATCHED_RISK",
  );
  const inscrutableItems = lineItems.filter(
    item => item.contentType === "INSCRUTABLE",
  );
  const connectiveItem =
    lineItems.filter(item => item.contentType === "CONNECTIVE")?.[0] ?? null;
  const sortedRuleMatches = lineItems
    .filter(item => item.contentType === "RULE_MATCH")
    .toSorted(
      sortBy(match => {
        const rule =
          versionedRules.find(r => r.id === match.versionedRuleId) ?? no();
        return inSequence(
          AnalysisResultRuleSortMap[rule.category],
          rule.text.toLowerCase(),
        );
      }),
    );
  const creatorClerkUser = hasValueFn(
    result.creator,
    u => clerkUserMap[u.clerkId],
  );

  return (
    <Card>
      <Flex direction="column" gap="3">
        <DataList.Root>
          <DataList.Item>
            <DataList.Label>Recommendation</DataList.Label>
            <DataList.Value>
              <Badge size="3" color={uiInfo?.color}>
                {uiInfo?.uiName}
              </Badge>
            </DataList.Value>
          </DataList.Item>
          {hv(result.overallAiReasoning) && (
            // PREPROD - add static reasoning text for outcomes
            <DataList.Item>
              <DataList.Label>Reasoning</DataList.Label>
              <DataList.Value>{result.overallAiReasoning}</DataList.Value>
            </DataList.Item>
          )}
          <DataList.Item>
            <DataList.Label>Confidence</DataList.Label>
            <DataList.Value>{result.overallConfidencePct}%</DataList.Value>
          </DataList.Item>
        </DataList.Root>
        <Flex
          justify="center"
          className="hover:bg-accent"
          style={{ cursor: "pointer" }}
          onClick={() => toggleDetail()}
          mt="1"
        >
          <Text mr="1">Full Breakdown</Text>
          {isShowingDetail ? <ChevronUp /> : <ChevronDown />}
        </Flex>
        {isShowingDetail && (
          <Flex asChild direction="column" gapY="5">
            <DataList.Root mt="2">
              <DataList.Item>
                <DataList.Label>Program</DataList.Label>
                <DataList.Value>{result.programName}</DataList.Value>
              </DataList.Item>
              <DataList.Item>
                <DataList.Label>Description</DataList.Label>
                <DataList.Value>
                  {result.programDescription ?? <em>(No description)</em>}
                </DataList.Value>
              </DataList.Item>
              <DataList.Item>
                <DataList.Label>Date</DataList.Label>
                <DataList.Value>
                  {format(result.createdAt, "MMM d, yyyy - h:mma")}
                </DataList.Value>
              </DataList.Item>
              <DataList.Item>
                <DataList.Label>Content</DataList.Label>
                <DataList.Value>
                  <Flex direction="column" align="start">
                    <AnnotatedUnderwritingContent
                      result={{ ...result, lineItems }}
                    />
                    <SubmittedRulesButton rules={versionedRules} />
                  </Flex>
                </DataList.Value>
              </DataList.Item>
              <DataList.Item>
                <DataList.Label>Source</DataList.Label>
                <DataList.Value>
                  {result.submissionSource === "USER"
                    ? creatorClerkUser!.fullName ??
                      creatorClerkUser!.emailAddress
                    : result.submissionSource === "API"
                    ? "API call"
                    : no()}
                </DataList.Value>
              </DataList.Item>
              {hv(adversarialItem) && (
                <DataList.Item>
                  <DataList.Label>
                    <Badge color={getLineItemColor(adversarialItem)}>
                      Adversarial
                    </Badge>
                  </DataList.Label>
                  <DataList.Value>
                    <Flex direction="column" gap="1">
                      <Text>{adversarialItem.reasoningText}</Text>
                      <Text>
                        <Badge color="gray" mr="1">
                          Confidence:
                        </Badge>
                        {adversarialItem.confidencePct}%
                      </Text>
                    </Flex>
                  </DataList.Value>
                </DataList.Item>
              )}
              {unmatchedItems.map(item => (
                <DataList.Item key={item.id}>
                  <DataList.Label>
                    <Badge color={getLineItemColor(item)}>
                      {(item.riskPct ?? no("No risk percent")) >=
                      riskThresholdPct
                        ? "New Risk"
                        : "Minimal Risk"}
                    </Badge>
                  </DataList.Label>
                  <DataList.Value>
                    <Flex direction="column" gap="1">
                      <Text>{joinQuotedText(item.textSegments)}</Text>
                      <Tooltip
                        content="The AI's estimate of the likelihood that you will
                              want to review this risk."
                      >
                        <Text style={{ cursor: "help" }}>
                          <Badge
                            color={getBadgeColorForRisk(
                              item.riskPct ?? softNo(0),
                            )}
                            mr="1"
                          >
                            Risk Estimate:
                          </Badge>
                          {item.riskPct}%
                        </Text>
                      </Tooltip>
                      <Text>
                        <Badge color="gray" mr="1">
                          Reasoning:
                        </Badge>{" "}
                        {item.reasoningText}
                      </Text>
                    </Flex>
                  </DataList.Value>
                </DataList.Item>
              ))}
              {inscrutableItems.map(item => (
                <DataList.Item key={item.id}>
                  <DataList.Label>
                    <Badge color={getLineItemColor(item)}>Unclear</Badge>
                  </DataList.Label>
                  <DataList.Value>
                    <Flex direction="column" gap="1">
                      <Text>{joinQuotedText(item.textSegments)}</Text>
                      <Text>
                        <Badge color="gray" mr="1">
                          Reasoning:
                        </Badge>
                        {item.reasoningText}
                      </Text>
                    </Flex>
                  </DataList.Value>
                </DataList.Item>
              ))}
              {sortedRuleMatches.map(item => {
                return (
                  <DataList.Item key={item.id}>
                    <DataList.Label>
                      <Badge color={getLineItemColor(item)}>
                        {RuleUiStrings[item.versionedRule!.category]}
                      </Badge>
                    </DataList.Label>
                    <DataList.Value>
                      <Flex direction="column" gap="1">
                        <Text>{joinQuotedText(item.textSegments)}</Text>
                        <Text>
                          <Badge color="gray" mr="1">
                            Reasoning:
                          </Badge>
                          {item.reasoningText}
                        </Text>
                        <Flex gap="2" align="center">
                          <Tooltip
                            content="The AI's estimate of the likelihood that this content 
                                represents significant risk beyond the rule as written."
                          >
                            <Text style={{ cursor: "help" }}>
                              <Badge
                                color={getBadgeColorForRisk(
                                  item.riskPct ?? softNo(0),
                                )}
                                mr="1"
                              >
                                Extra Risk:
                              </Badge>
                              {item.riskPct}%
                            </Text>
                          </Tooltip>
                          <Text>
                            <Badge color="gray" mr="1">
                              Confidence:
                            </Badge>
                            {item.confidencePct}%
                          </Text>
                        </Flex>
                        <ShowRuleButton rule={item.versionedRule ?? no()} />
                      </Flex>
                    </DataList.Value>
                  </DataList.Item>
                );
              })}
              {hv(connectiveItem) && (
                <DataList.Item key="Connective">
                  <DataList.Label>
                    <Badge color={getLineItemColor(connectiveItem)}>
                      Connective Text
                    </Badge>
                  </DataList.Label>
                  <DataList.Value>
                    {connectiveItem.textSegments.map(s => `"${s}"`).join(" ")}
                  </DataList.Value>
                </DataList.Item>
              )}
            </DataList.Root>
          </Flex>
        )}
      </Flex>
    </Card>
  );
}

function getBadgeColorForRisk(excessRisk: number): BadgeColor {
  if (excessRisk < 10) {
    return "gray";
  }

  if (excessRisk < 50) {
    return "orange";
  }

  return "red";
}
