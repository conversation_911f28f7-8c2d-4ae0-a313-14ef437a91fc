import React from "react";
import { nodeHasContent } from "~/Tools";

export function Dividers(props: {
  children: React.ReactNode;
  divider?: React.ReactNode | ((i: number) => React.ReactNode);
}) {
  const renderDivider = (i: number) => {
    if (i === 0) {
      return null;
    }
    if (!nodeHasContent(props.divider)) {
      return <hr />;
    }
    if (props.divider instanceof Function) {
      return props.divider(i);
    }
    return props.divider;
  };
  return (
    <>
      {React.Children.toArray(props.children)
        .filter(e => nodeHasContent(e))
        .map((e, i) => (
          <React.Fragment key={i}>
            {renderDivider(i)}
            {e}
          </React.Fragment>
        ))}
    </>
  );
}
