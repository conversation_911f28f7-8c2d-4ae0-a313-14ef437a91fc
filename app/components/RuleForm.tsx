import {
  FlatMultiplierType,
  PercentMultiplierType,
  RuleCategory,
} from "prisma/interfaces";
import {
  Button,
  Flex,
  Select,
  Text,
  TextArea,
  TextField,
} from "@radix-ui/themes";
import { z } from "zod";
import {
  flatMultplierTypeInfo,
  percentMultiplierTypeInfo,
  RuleSortMap,
  RuleUiStrings,
} from "~/Constants";
import { createRuleSchema } from "~/routes/underwritingPrograms.$id";
import { excludeEnum } from "~/Tools";
import { useField, useZodForm } from "~/utils/formHelpers";
import { zx } from "~/utils/validationHelpers";
import { Field } from "./Field";

export const ruleFormSchema = z.intersection(
  z.object({
    text: zx.saneString(),
  }),
  z.union([
    z.object({
      category: z.literal(RuleCategory.FLAT_PREMIUM),
      amount: z.number(),
      flatMultiplierType: z.nativeEnum(FlatMultiplierType),
    }),
    z.object({
      category: z.literal(RuleCategory.PERCENT_PREMIUM),
      percentage: z.number(),
      percentMultiplierType: z.nativeEnum(PercentMultiplierType),
    }),
    z.object({
      category: z.enum(
        excludeEnum(RuleCategory, [
          RuleCategory.FLAT_PREMIUM,
          RuleCategory.PERCENT_PREMIUM,
        ]),
      ),
    }),
  ]),
);

type RuleFormProps = {
  loading?: boolean;
  onCancel: () => void;
  value?: z.input<typeof ruleFormSchema>;
  onSubmit: (data: z.input<typeof createRuleSchema>) => Promise<void>;
  submitText: React.ReactNode;
};

export function RuleForm({
  loading,
  onCancel,
  value,
  onSubmit,
  submitText,
}: RuleFormProps) {
  const form = useZodForm(ruleFormSchema, {
    defaultValues: { category: RuleCategory.INCLUDE, text: "", ...value },
  });

  const categoryField = useField(form.control, "category");
  const flatMultiplierTypeField = useField(form.control, "flatMultiplierType");
  const percentMultiplierTypeField = useField(
    form.control,
    "percentMultiplierType",
  );
  return (
    <form
      onSubmit={form.handleSubmit(async values => {
        let submitValues;
        if (values.category === "FLAT_PREMIUM") {
          submitValues = {
            ...values,
            // Convert dollars and cents to cents
            basisValue: Math.round(values.amount * 100),
          };
        } else if (values.category === "PERCENT_PREMIUM") {
          submitValues = {
            ...values,
            // Convert percentage to basis points (e.g., 12.34% -> 1234)
            basisValue: Math.round(values.percentage * 100),
          };
        } else {
          submitValues = values;
        }
        await onSubmit(submitValues);
      })}
    >
      <Flex direction="column" gap="4">
        <Field label="Category">
          <Select.Root
            {...categoryField}
            onValueChange={categoryField.onChange}
          >
            <Select.Trigger placeholder="Select category" />
            <Select.Content position="popper">
              {Object.keys(RuleSortMap).map(k => (
                <Select.Item value={k} key={k}>
                  {RuleUiStrings[k]}
                </Select.Item>
              ))}
            </Select.Content>
          </Select.Root>
        </Field>
        {categoryField.value === "FLAT_PREMIUM" && (
          <>
            <Field label="Amount">
              <TextField.Root
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                required
                {...form.register("amount", { valueAsNumber: true })}
              >
                <TextField.Slot>
                  <Text size="2">$</Text>
                </TextField.Slot>
              </TextField.Root>
            </Field>
            <Field label="Multiplier Type">
              <Select.Root
                {...flatMultiplierTypeField}
                onValueChange={flatMultiplierTypeField.onChange}
              >
                <Select.Trigger placeholder="Select multiplier type" />
                <Select.Content position="popper">
                  {Object.entries(flatMultplierTypeInfo).map(([k, v]) => (
                    <Select.Item value={k} key={k}>
                      {v.uiString}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select.Root>
            </Field>
          </>
        )}
        {categoryField.value === "PERCENT_PREMIUM" && (
          <>
            <Field label="Percentage">
              <TextField.Root
                type="number"
                step="0.01"
                min="0"
                placeholder="0.00"
                required
                {...form.register("percentage", { valueAsNumber: true })}
              >
                <TextField.Slot>
                  <Text size="2">%</Text>
                </TextField.Slot>
              </TextField.Root>
            </Field>
            <Field label="Multiplier Type">
              <Select.Root
                {...percentMultiplierTypeField}
                onValueChange={percentMultiplierTypeField.onChange}
              >
                <Select.Trigger placeholder="Select multiplier type" />
                <Select.Content position="popper">
                  {Object.entries(percentMultiplierTypeInfo).map(([k, v]) => (
                    <Select.Item value={k} key={k}>
                      {v.uiString}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select.Root>
            </Field>
          </>
        )}

        <Field label="Rule Text">
          <TextArea
            id="text"
            placeholder="Enter the rule description"
            required
            rows={6}
            {...form.register("text")}
          />
        </Field>

        <Flex justify="end" gap="2">
          <Button type="submit" loading={loading}>
            {submitText}
          </Button>
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        </Flex>
      </Flex>
    </form>
  );
}
