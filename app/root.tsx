import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/react-router";
import { rootAuth<PERSON>oader } from "@clerk/react-router/ssr.server";
import { Theme } from "@radix-ui/themes";
import "@radix-ui/themes/styles.css";
import "app/app.css";
import React from "react";
import {
  Links,
  LinksFunction,
  Meta,
  Outlet,
  redirect,
  Scripts,
  ScrollRestoration,
} from "react-router";
import type { Route } from "./+types/root";
import { hv } from "./Tools";
import { envPublic } from "./env";
import { envSecret } from "./env.server";
import { CURRENT_PRODUCER_CONFIG } from "./producers";
import "./tailwind.css";
import { clerkClient } from "./utils/clerkHelpers.server";
import { prisma } from "./utils/db.server";
import { PostHogProvider } from "posthog-js/react";

export const links: LinksFunction = () => [
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  {
    rel: "preconnect",
    href: "https://fonts.gstatic.com",
    crossOrigin: "anonymous",
  },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap",
  },
];

export function meta(_: Route.MetaArgs): Route.MetaDescriptors {
  return [
    {
      tagName: "link",
      rel: "icon",
      href: `/${CURRENT_PRODUCER_CONFIG.faviconFilename}`,
      type: "image/x-icon",
    },
  ];
}

export async function loader(args: Route.LoaderArgs) {
  const noAuthRoutes = ["/logIn", "/programs"];

  const pathname = new URL(args.request.url).pathname;
  if (pathname === "/") {
    return redirect("/home");
  }
  return rootAuthLoader(
    args,
    async ({ request }) => {
      const url = new URL(request.url);
      const redirectTo = url.pathname + url.search;
      const { userId, orgId } = request.auth;
      if (
        !hv(userId) &&
        !noAuthRoutes.some(r =>
          url.pathname.toLowerCase().startsWith(r.toLowerCase()),
        )
      ) {
        throw redirect(`/logIn?redirectTo=${encodeURIComponent(redirectTo)}`);
      }
      if (hv(userId)) {
        // PREPROD: create user and organization entries on login if there isn't one
        // this is so we only need to configure these in clerk
        const user = await clerkClient.users.getUser(userId);
        await prisma.user.upsert({
          create: { clerkId: user.id },
          update: {},
          where: { clerkId: user.id },
        });
        const memberships =
          await clerkClient.users.getOrganizationMembershipList({ userId });
        for (const { organization } of memberships.data) {
          await prisma.organization.upsert({
            create: { clerkId: organization.id, name: organization.name },
            update: {},
            where: { clerkId: organization.id },
          });
        }
        if (
          !hv(orgId) &&
          !url.pathname
            .toLowerCase()
            .startsWith("/selectOrganization".toLowerCase())
        ) {
          throw redirect(
            `/selectOrganization?redirectTo=${encodeURIComponent(redirectTo)}`,
          );
        }
      }
      return null;
    },
    {
      publishableKey: envPublic.VITE_CLERK_PUBLISHABLE_KEY,
      secretKey: envSecret.CLERK_SECRET_KEY,
    },
  );
}

export function Layout({ children }: { children: React.ReactNode }) {
  const producer = CURRENT_PRODUCER_CONFIG;
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body style={{ minHeight: "100vh" }}>
        <Theme
          appearance={producer.appearanceMode}
          data-accent-color="primary"
          asChild
          style={{
            "--custom-primary-color": producer.primaryColor,
            "--color-background": producer.siteBackgroundColor,
          }}
        >
          <div
            style={{
              height: "max(100vh, 100%)",
              display: "flex",
              flexDirection: "column",
            }}
          >
            {children}
            {/* <ThemePanel /> */}
            <ScrollRestoration />
            <Scripts />
          </div>
        </Theme>
      </body>
    </html>
  );
}

export default function App({ loaderData }: Route.ComponentProps) {
  const producer = CURRENT_PRODUCER_CONFIG;
  return (
    <ClerkProvider
      loaderData={loaderData}
      publishableKey={envPublic.VITE_CLERK_PUBLISHABLE_KEY}
      appearance={{
        baseTheme: producer.clerkTheme,
      }}
    >
      <PostHogProvider
        apiKey={envPublic.VITE_POSTHOG_PUBLIC_KEY}
        options={{
          api_host: envPublic.VITE_POSTHOG_PUBLIC_HOST,
          disable_compression: import.meta.env.DEV,
        }}
      >
        <Outlet />
      </PostHogProvider>
    </ClerkProvider>
  );
}
