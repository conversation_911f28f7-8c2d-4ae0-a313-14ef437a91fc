import { EquipmentApplication } from "~/EquipmentProgram/equipmentJsonSchema";
import { FullCodeValidationResult } from "~/Tools";
import { runIsolatedCode } from "~/utils/isolate.server";
import privateValidationCodeTs from "../EquipmentProgram/equipmentProgramPrivateFunctions.ts?raw";

export async function isolatePrivateValidateEquipmentApp(
  equipmentApp: EquipmentApplication,
) {
  console.log("ISOLATING private code...");

  const result = await runIsolatedCode<FullCodeValidationResult>(
    privateValidationCodeTs,
    "privateValidateEquipmentApplication",
    equipmentApp,
  );

  console.log(
    `DONE with PRIVATE code.  (isEverythingValid: ${result?.isEverythingValid})`,
  );

  return result;
}
