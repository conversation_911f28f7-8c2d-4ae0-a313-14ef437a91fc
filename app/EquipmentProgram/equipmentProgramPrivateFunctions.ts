import { FullCodeValidationResult, SingleCodeValidationResult } from "~/Tools";

import { EquipmentApplication } from "./equipmentJsonSchema";

export function privateValidateEquipmentApplication(
  app: EquipmentApplication,
): FullCodeValidationResult {
  const result: SingleCodeValidationResult = [
    "10009",
    "33166",
    "48187",
  ].includes(app.mailingAddress.zipCode)
    ? {
        detailedErrorMessage: "Prohibited zip code",
        isValid: false,
      }
    : { detailedErrorMessage: null, isValid: true };

  return {
    isEverythingValid: result.isValid,
    results: [result],
  };
}
