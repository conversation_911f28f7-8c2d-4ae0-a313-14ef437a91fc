import type {
  FullCodeValidationResult,
  SingleCodeValidationResult,
} from "~/Tools";
import {
  addCalendarDays,
  calculateAgeOnDate,
  verifyIsAfter,
  verifyIsBefore,
} from "../utils/aiStandardLibrary";
import { EquipmentApplication } from "./equipmentJsonSchema";

export function validateEquipmentProgramApplication(
  app: EquipmentApplication,
): FullCodeValidationResult {
  const results: SingleCodeValidationResult[] = [
    validateAge(
      app.contactInformation.dateOfBirth,
      app.coverageDetails.startDate,
    ),
    validateCoverageStartDate(
      app.coverageDetails.startDate,
      app.applicationDate,
    ),
    ...(app.equipmentDamages.damages?.map(d =>
      validateConditionalDamageDate(d.damageDate, app.applicationDate),
    ) ?? []),
    verifyTotalCoverage(
      app.coverageDetails.ownedEquipment,
      app.coverageDetails.rentedEquipment,
      app.coverageDetails.rentalReimbursement,
    ),
    verifyStateBusinessType(
      app.mailingAddress.state,
      app.companyInformation.companyName,
    ),
  ];

  const isEverythingValid = results.every(r => r.isValid);

  return { isEverythingValid, results };
}

export function validateAge(
  birthDate: string,
  coverageStartDate: string,
): SingleCodeValidationResult {
  const ageAtCoverageStart = calculateAgeOnDate({
    birthDate,
    referenceDate: coverageStartDate,
  });
  const isValid = ageAtCoverageStart >= 18;
  return {
    isValid,
    detailedErrorMessage: isValid
      ? null
      : `Applicant must be at least 18 years old at coverage start. (Actual age at coverage start: ${ageAtCoverageStart}.)`,
  };
}

export function validateCoverageStartDate(
  coverageStartDate: string,
  applicationDate: string,
): SingleCodeValidationResult {
  const isValid = verifyIsAfter({
    futureDate: coverageStartDate,
    referenceDate: applicationDate,
    shouldIncludeReferenceDate: true,
    maxDaysInFuture: 30,
  });
  const latestValidStartDate = addCalendarDays(applicationDate, 30);
  return {
    isValid,
    detailedErrorMessage: isValid
      ? null
      : `Coverage must start within 30 days of Application Date. (Latest valid start date: ${latestValidStartDate}}`,
  };
}

export function validateConditionalDamageDate(
  damageDate: string,
  applicationDate: string,
): SingleCodeValidationResult {
  const isValid = verifyIsBefore({
    pastDate: damageDate,
    referenceDate: applicationDate,
    shouldIncludeReferenceDate: true,
  });
  return {
    isValid,
    detailedErrorMessage: isValid
      ? null
      : "Damage date(s) must be on or before the Application Date",
  };
}

export function verifyTotalCoverage(
  ownedCoverageAmount: number,
  rentedCoverageAmount: number,
  rentalReimbursementAmount: number,
): SingleCodeValidationResult {
  const totalCoverage =
    ownedCoverageAmount + rentedCoverageAmount + rentalReimbursementAmount;

  if (totalCoverage === 0) {
    return {
      isValid: false,
      detailedErrorMessage: `Total coverage amount (Rented + Owned) cannot be zero.`,
    };
  }

  if (totalCoverage > 1_000_000) {
    return {
      isValid: false,
      detailedErrorMessage: `Total coverage amount (Rented + Owned + Rental Reimbursement = ${totalCoverage}) cannot exceed $1,000,000.`,
    };
  }

  return { isValid: true, detailedErrorMessage: null };
}

export function verifyStateBusinessType(
  state: string,
  businessType: string,
): SingleCodeValidationResult {
  const noOtherStates = ["CA", "NY"];
  if (noOtherStates.includes(state) && businessType === "Other") {
    return {
      isValid: false,
      detailedErrorMessage: `Business type 'Other' is not available in these states: ${noOtherStates.join(
        ", ",
      )}`,
    };
  }

  return {
    isValid: true,
    detailedErrorMessage: null,
  };
}
