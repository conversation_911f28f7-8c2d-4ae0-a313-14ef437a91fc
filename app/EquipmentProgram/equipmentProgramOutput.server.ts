import { EquipmentApplication } from "./equipmentJsonSchema";

export function outputEquipmentValues(app: EquipmentApplication): string {
  const totalCoverage =
    app.coverageDetails.ownedEquipment +
    app.coverageDetails.rentedEquipment +
    app.coverageDetails.rentalReimbursement;
  const adminFee = 25;
  const basePremium = totalCoverage * 0.05;
  const totalCost = basePremium + adminFee;
  return /*md*/ `
Admin Fee: $${adminFee}

Base Premium: $${basePremium}

Total Cost: $${totalCost}
`;
}
