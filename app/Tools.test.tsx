import { expect, test } from "vitest";
import { fillTemplateString } from "./Tools";

test("fillTemplateString", () => {
  expect(() =>
    fillTemplateString("missing field", { someField: "val" }),
  ).toThrow();

  expect(fillTemplateString("{{f}} {{f}}", { f: "filled" })).toEqual(
    "filled filled",
  );

  // Malformed - must be only word chars
  expect(() => fillTemplateString("{{  }}", {})).toThrow();

  // Malformed - must be only word chars
  expect(() => fillTemplateString("{{}}", {})).toThrow();

  // Malformed - no closing
  expect(() => fillTemplateString("{{field1}} {{field2", {})).toThrow();

  // Missing field in object
  expect(() =>
    fillTemplateString("{{okField}} {{missingField}}", { okField: "filled" }),
  ).toThrow();
  expect(() => fillTemplateString("{{missingField}}", {})).toThrow();

  // Missing field in template
  expect(() =>
    fillTemplateString("{{okField}}", {
      okField: "filled",
      missingField: "filled",
    }),
  ).toThrow();

  expect(
    fillTemplateString("{{boolLogic}}{{spacing}}{{whichDay}}", {
      whichDay: "today",
      boolLogic: "not",
      spacing: " ",
    }),
  ).toEqual("not today");
});
