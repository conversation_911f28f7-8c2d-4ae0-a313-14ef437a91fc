import { useFetcher } from "react-router";
import { AnalyzeActionFn } from "~/routes/api.analyze";

export function useAnalyzeFetcher() {
  return useFetcher<AnalyzeActionFn>();
}

export type ClientAnalysisResult = Extract<
  ReturnType<typeof useAnalyzeFetcher>["data"],
  { success: true }
>["analysisResult"];

export type ClientVersionedRule = Extract<
  ReturnType<typeof useAnalyzeFetcher>["data"],
  { success: true }
>["versionedRules"][0];
