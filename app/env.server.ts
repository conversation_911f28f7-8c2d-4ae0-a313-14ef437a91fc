import { z } from "zod";

const envSchema = z.object({
  NODE_ENV: z
    .enum(["production", "development", "test"])
    .default("development"),
  DATABASE_URL: z.string(),
  // Clerk expects these env keys exactly, o/w it requires some nontrivial configuration
  CLERK_SECRET_KEY: z.string(),
  LANGCHAIN_LOG_LEVEL: z.enum(["NONE", "VERBOSE"]).default("NONE"),
  ANTHROPIC_API_KEY: z.string(),
  OPENAI_API_KEY: z.string(),
  POSTMARK_API_KEY: z.string(),
  DEBUG_EMAIL_DROPOFF_DIRECTORY: z.string().optional(),
});
export const envSecret = envSchema.parse(process.env);
