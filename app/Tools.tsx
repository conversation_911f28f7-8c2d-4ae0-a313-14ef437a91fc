import { Tool } from "@anthropic-ai/sdk/resources/index.mjs";
import { useDocumentTitle } from "@uidotdev/usehooks";
import addFormats from "ajv-formats";
import Ajv2020 from "ajv/dist/2020";
// zodToJsonSchema supports only up to 2019 (as of Feb 2025) so we use that.
// This seems fine since JSON Schema 2020 seems to have very minor changes: https://json-schema.org/draft/2020-12/release-notes

import { useMemo, useRef, useState } from "react";
import { Temporal } from "temporal-polyfill";
import { ZodSchema, ZodType } from "zod";
import { Options, Targets, zodToJsonSchema } from "zod-to-json-schema";

// NOTE: these scalars are interconvertible with number, but not each other
export type Dollars = number & { __numType?: "dollars" };
export type Cents = number & { __numType?: "cents" };
export type Proportion = number & { __numType?: "proportion" };
export type BasisPoints = number & { __numType?: "basisPoint" };

// Ensures unnecessary checks aren't performed
type NullPart<T> = T & (null | undefined);
export type MustBeAmbiguouslyNullable<T> = NullPart<T> extends never
  ? never
  : NonNullable<T> extends never
  ? never
  : T;

export function hasValue<T>(
  value: MustBeAmbiguouslyNullable<T>,
): value is NonNullable<MustBeAmbiguouslyNullable<T>> {
  return (value as unknown) !== undefined && (value as unknown) !== null;
}

export const hv = hasValue;

export function hasValueFn<T, A>(
  value: MustBeAmbiguouslyNullable<T>,
  thenFn: (value: NonNullable<MustBeAmbiguouslyNullable<T>>) => A,
): A | NullPart<T> {
  return hasValue(value)
    ? thenFn(value)
    : (value as NullPart<MustBeAmbiguouslyNullable<T>>);
}

export class Assert {
  static hard(
    condition: boolean | undefined | null,
    message: string,
    ...optionalParams: unknown[]
  ): asserts condition {
    if (condition !== true) {
      const assertionMessage = `Hard assertion failed: ${message}`;
      console.warn(assertionMessage, ...optionalParams);
      throw new Error(assertionMessage);
    }
  }

  static soft(
    condition: boolean | undefined | null,
    message: string,
    ...optionalParams: unknown[]
  ) {
    if (condition !== true) {
      const assertionMessage = `Soft assertion failed: ${message}`;
      console.warn(assertionMessage, ...optionalParams);
    }
  }

  /** throws an "unreachable" error, use this when typescript fails to discriminate a type union after calling hasValue */
  static unreachableUnionHasValue(): never {
    Assert.hard(false, "unreachable");
  }

  static hasValue<T>(value: MustBeAmbiguouslyNullable<T>): NonNullable<T> {
    if (!hasValue(value)) {
      throw new Error("Unexpected null or undefined");
    }

    return value;
  }
}

const usdFormatterWithCents = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  minimumFractionDigits: 2,
});

export function formatDollarsAndCents(totalCents: Cents) {
  Assert.soft(
    Number.isInteger(totalCents),
    `${totalCents} is not a whole cent amount`,
  );

  const absCents = Math.abs(totalCents);
  const formatted = usdFormatterWithCents.format(absCents / 100);

  // parens for negative
  return totalCents >= 0 ? formatted : `(${formatted})`;
}

export function formatDollarsAndOptionalCents(totalCents: Cents) {
  if (totalCents % 100 === 0) {
    return formatWholeDollars(totalCents);
  } else {
    return formatDollarsAndCents(totalCents);
  }
}

export function formatPercent(proportion: Proportion) {
  return proportion.toLocaleString(undefined, {
    style: "percent",
    minimumFractionDigits: 2,
  });
}

export function formatBasisPoints(basisPoints: BasisPoints) {
  return (basisPoints / 10000).toLocaleString(undefined, {
    style: "percent",
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });
}

const compactUsdFormatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  notation: "compact",
  ...{ compactDisplay: "short" },
});

export function formatDollarsCompact(totalCents: Cents) {
  return compactUsdFormatter.format(totalCents / 100).toLowerCase();
}

const wholeUsdFormatter = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
  minimumFractionDigits: 0,
  maximumFractionDigits: 0,
});

export function formatWholeDollars(totalCents: Cents) {
  const dollars = totalCents / 100;
  Assert.soft(
    Number.isInteger(dollars),
    `${dollars} is not a whole dollar amount`,
  );
  return wholeUsdFormatter.format(totalCents / 100);
}

export function no(message = "no() called"): never {
  throw new Error(message);
}

export function unreachable(_: never): never {
  throw new Error("unreachable implied by type, check type declaration");
}

export function typecheckAssign<A, _B extends A>() {}

// Logs the issue and continues execution.  Similar to soft assert
export function softNo<T>(fallbackValue: T, message?: string): T {
  // Hook up to Sentry or similar when available
  console.error(message ?? "noFallback called");

  return fallbackValue;
}

export function useCounterKey(start = 0): [number, () => void] {
  const [count, setCount] = useState(start);
  return [count, () => setCount(k => k + 1)];
}

export type ZodParsedSchemaType<T extends ZodSchema<unknown>> = ReturnType<
  T["parse"]
>;

export function nodeHasContent(value: React.ReactNode | object) {
  return (
    value !== undefined &&
    value !== null &&
    value !== true &&
    value !== false &&
    value !== ""
  );
}

export function dedup<T, U>(items: T[], keyGetter?: (value: T) => U) {
  if (keyGetter === undefined) {
    return [...new Set(items)];
  } else {
    const reduceResult = items.reduce<{ arr: T[]; keySet: Set<U> }>(
      (acc, item) => {
        const newKey = keyGetter(item);
        if (acc.keySet.has(newKey)) {
          return acc;
        }

        // new item
        acc.keySet.add(newKey);

        return {
          arr: [...acc.arr, item],
          keySet: acc.keySet,
        };
      },

      { arr: [], keySet: new Set<U>() },
    );

    return reduceResult.arr;
  }
}

export function pretty(obj: unknown) {
  return JSON.stringify(
    obj,
    (_, value) => (typeof value === "function" ? "Function" : value),
    2,
  );
}

export function logify(obj: unknown) {
  console.log(pretty(obj));
}

export function deepEqual(a: unknown, b: unknown): boolean {
  if (Array.isArray(a) && Array.isArray(b)) {
    return a.length === b.length && a.every((a, i) => deepEqual(a, b[i]));
  } else if (typeof a === "object" && typeof b === "object" && hv(a) && hv(b)) {
    return (
      Object.keys(a).length === Object.keys(b).length &&
      Object.keys(a).every(k => deepEqual(a[k], b[k]))
    );
  } else {
    return a === b;
  }
}

type NonEmptyArray<T> = [T, ...T[]];

export function excludeEnum<T extends string, ExcluedT extends T>(
  enumObj: Record<T, T>,
  args: ExcluedT[],
) {
  return Object.values(enumObj).filter(
    e => !args.includes(e as ExcluedT),
  ) as unknown as NonEmptyArray<Exclude<T, ExcluedT>>;
}

function useMemoWithState<S, T>(
  f: (prevState: S | null) => { state: S | null; result: T },
) {
  const state = useRef(null as S | null);
  return useMemo(() => {
    const { state: newState, result } = f(state.current);
    state.current = newState;
    return result;
  }, [f]);
}
export function useLastNonNull<S>(f: () => S | null) {
  return useMemoWithState((prevState: S | null) => {
    const state = f() ?? prevState;
    return { state, result: state };
  });
}

export const Ajv = new Ajv2020({ strict: false, allErrors: true }); // allow custom fields
// addFormats() supports **date, uri, email, int32, double...**
// full list at https://ajv.js.org/packages/ajv-formats.html#formats
addFormats(Ajv);

export const jsonSchema2020Url = "https://json-schema.org/draft/2020-12/schema";

export function zodToJsonSchema2020<Target extends Targets = "jsonSchema7">(
  zodSchema: ZodType,
  options?: Partial<Options<Target>>,
) {
  const jsonSchema = zodToJsonSchema(zodSchema, options);
  return {
    ...jsonSchema,
    $schema: jsonSchema2020Url,
  } as typeof jsonSchema;
}

export function fillTemplateString(
  stringToFill: string,
  fillFields: Record<string, string>,
) {
  // Check for malformed fields (unclosed braces)
  const openCount = stringToFill.match(/\{\{/g)?.length ?? 0;
  const closeCount = stringToFill.match(/\}\}/g)?.length ?? 0;

  Assert.hard(openCount === closeCount, "Malformed template: unclosed braces");

  // Find all template fields in the string (including empty ones)
  const templateFieldRegex = /\{\{([^{}]*)\}\}/g;
  const matches = [...stringToFill.matchAll(templateFieldRegex)];

  // Extract all field names from the template and trim whitespace
  const templateFieldNames = [
    ...new Set(matches.map(match => match[1]?.trim() ?? "")),
  ];

  // Check for malformed fields (empty or containing invalid characters)
  for (const fieldName of templateFieldNames) {
    if (fieldName === "" || !/^\w+$/.test(fieldName)) {
      throw new Error(`Malformed template field: ${fieldName}`);
    }
  }

  // Check that all fields in the template are provided in fillFields
  for (const fieldName of templateFieldNames) {
    if (!(fieldName in fillFields)) {
      throw new Error(`Did not find value for field ${fieldName}`);
    }
  }

  // Check that all fields in fillFields are used in the template
  for (const fieldName of Object.keys(fillFields)) {
    if (!templateFieldNames.includes(fieldName)) {
      throw new Error(`Unused field in fillFields: ${fieldName}`);
    }
  }

  // Replace all occurrences of each field using reduce
  return Object.entries(fillFields).reduce(
    (result, [fieldName, fieldContent]) => {
      const replaceStr = `{{${fieldName}}}`;
      return result.replaceAll(replaceStr, fieldContent);
    },
    stringToFill,
  );
}

export function useUzeroTitle(title: string) {
  useDocumentTitle(`${title} - UZero`);
}

export function last<T>(items: T[]): T | undefined {
  return items.slice(-1)[0];
}

// waiting on browser support: https://caniuse.com/?search=stream%20asynciterator
export async function* streamIter<T>(stream: ReadableStream<T>) {
  const reader = stream.getReader();
  try {
    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        return;
      }
      yield value;
    }
  } finally {
    reader.releaseLock();
  }
}

export function memoizeResults<A, B>(f: (input: A) => Promise<B>) {
  const cache = new Map<A, B>();
  return async (input: A) => {
    let output = cache.get(input);
    if (output !== undefined) {
      return output;
    }
    output = await f(input);
    cache.set(input, output);
    return output;
  };
}

export type TypescriptSource = string & {
  __tag: "TypescriptSource" | undefined;
};
/**
 * Transforms TypeScript code to JavaScript.
 * Handles esbuild initialization if needed.
 */
// We should memoize here, since wasm can't free memory,
// and therefore we should never transform more than once per source file
export const transformTsToJs = memoizeResults(async function (
  tsCode: TypescriptSource,
): Promise<string> {
  // Import esbuild-wasm dynamically to avoid client-side imports
  const esbuild = await import("esbuild-wasm");

  try {
    // Try to transform directly first
    const result = await esbuild.transform(tsCode, { loader: "ts" });
    return result.code;
  } catch (error: unknown) {
    // Check if the error is about initialization
    if (
      typeof error === "object" &&
      error !== null &&
      "message" in error &&
      typeof error.message === "string" &&
      error.message.includes("initialize")
    ) {
      // Initialize esbuild and try again
      await esbuild.initialize({});
      const result = await esbuild.transform(tsCode, { loader: "ts" });
      return result.code;
    } else {
      // Re-throw any other errors
      throw error;
    }
  }
});

export function getSchemaErrors<T>(input: T, schema: Tool.InputSchema) {
  const compiledSchema = Ajv.compile(schema);
  compiledSchema(input);
  return compiledSchema.errors ?? null;
}

export type SingleCodeValidationResult = {
  isValid: boolean;
  detailedErrorMessage: string | null;
};

export type FullCodeValidationResult = {
  isEverythingValid: boolean;
  results: SingleCodeValidationResult[];
};

export async function* asyncIteratorToIterable<T>(iter: AsyncIterator<T>) {
  while (true) {
    const { done, value } = await iter.next();
    if (done === true) {
      return;
    }
    yield value;
  }
}

export function formatLongDate(date: Temporal.PlainDate): string {
  const month = date.toLocaleString("en-US", { month: "long" });
  const day = date.day;
  const year = date.year;

  return `${month} ${day}, ${year}`;
}

export function asyncGeneratorToStream<T>(iter: AsyncGenerator<T>) {
  const stream = new ReadableStream({
    async start(controller) {
      for await (const data of iter) {
        controller.enqueue(data);
      }
      controller.close();
    },
  });
  return stream;
}

export type Union<T, Key = keyof T> = Key extends keyof T
  ? { [K in Exclude<keyof T, Key>]?: undefined } & { [K in Key]: T[K] }
  : never;

// Since react-router only provides us with the Request abstraction (and not NodeJS.Request)
// this only gets an ip address when proxied, i.e. returns undefined when run in dev
export function getIpAddress(request: Request) {
  // Accd to Wikipedia, the FIRST item in this comma-separated header
  // list should be the original Client (order is: client, proxy1, proxy2...)
  // https://en.wikipedia.org/wiki/X-Forwarded-For
  // It appears Heroku will only ever give us a single IP here, but
  // implementing completely with array split for robustness.
  const fwdHeader = request.headers.get("x-forwarded-for");
  const ipAddress = hv(fwdHeader)
    ? last(fwdHeader.split(","))?.trim()
    : undefined;
  return ipAddress;
}
