import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ing, <PERSON><PERSON><PERSON><PERSON>on } from "@radix-ui/themes";
import { ArrowLeft } from "lucide-react";
import { data, redirect, useFetcher } from "react-router";
import { LoggedInWrapper } from "~/components/LoggedInWrapper";
import { deepEqual } from "~/Tools";
import { requireAuth } from "~/utils/authHelpers.server";
import { prisma } from "~/utils/db.server";
import { parseFormData } from "~/utils/schemaHelpers.server";
import type { Route } from "./+types/createProgram";
import {
  DiscardAlert,
  EditProgramFields,
  editProgramSchema,
} from "./editProgram.$id";
import { useZodForm } from "~/utils/formHelpers";

export async function action(args: Route.ActionArgs) {
  const clerkAuth = await requireAuth(args);
  const { request } = args;

  const result = await parseFormData(request, editProgramSchema);
  if (!result.success) {
    return data({ success: false, errors: result.error }, { status: 400 });
  }
  await prisma.program.create({
    data: {
      name: result.data.name,
      descriptionText: result.data.description,
      organization: { connect: { clerkId: clerkAuth.orgId } },
    },
  });
  return redirect("/underwritingPrograms");
}

function CreateProgramPage() {
  const fetcher = useFetcher<typeof action>();
  const {
    register,
    handleSubmit,
    getValues,
    formState: { defaultValues },
  } = useZodForm(editProgramSchema, {
    defaultValues: {
      name: "",
      description: "",
    },
  });

  const getIsDirty = () => !deepEqual(getValues(), defaultValues);
  return (
    <Flex gap="3" direction="column" mt="3">
      <Flex align="center" gap="3">
        <DiscardAlert getIsDirty={getIsDirty}>
          <IconButton>
            <ArrowLeft />
          </IconButton>
        </DiscardAlert>
        <Heading>Create New Program</Heading>
      </Flex>
      <form
        onSubmit={handleSubmit(async d => {
          await fetcher.submit(d, {
            method: "POST",
            encType: "application/json",
          });
        })}
      >
        <Flex direction="column" gap="3">
          <EditProgramFields register={register} />
          <Flex gap="2">
            <Button type="submit" loading={fetcher.state === "submitting"}>
              Create
            </Button>
            <DiscardAlert getIsDirty={getIsDirty}>
              <Button
                type="button"
                variant="outline"
                loading={fetcher.state === "submitting"}
              >
                Cancel
              </Button>
            </DiscardAlert>
          </Flex>
        </Flex>
      </form>
    </Flex>
  );
}

export default function WrappedPage() {
  return (
    <LoggedInWrapper>
      <CreateProgramPage />
    </LoggedInWrapper>
  );
}
