import {
  AlertDialog,
  Box,
  Button,
  Flex,
  Heading,
  IconButton,
  TextArea,
  TextField,
  VisuallyHidden,
} from "@radix-ui/themes";
import { ArrowLeft } from "lucide-react";
import { UseFormRegister } from "react-hook-form";
import { data, Link, redirect, useFetcher, useNavigate } from "react-router";
import { z } from "zod";
import { LoggedInWrapper } from "~/components/LoggedInWrapper";
import { deepEqual, hv, no } from "~/Tools";
import { requireAuth } from "~/utils/authHelpers.server";
import { prisma } from "~/utils/db.server";
import { safeData } from "~/utils/restHelpers";
import { parseFormData } from "~/utils/schemaHelpers.server";
import { zx } from "~/utils/validationHelpers";
import type { Route } from "./+types/editProgram.$id";
import { useReportServerError, useZodForm } from "~/utils/formHelpers";
import { Field } from "~/components/Field";

export const editProgramSchema = z.object({
  name: zx.saneString(),
  description: zx.maybe(zx.saneString()),
});

export function EditProgramFields({
  register,
}: {
  register: UseFormRegister<z.infer<typeof editProgramSchema>>;
}) {
  return (
    <>
      <Field label="Name">
        <TextField.Root {...register("name")} required />
      </Field>
      <Field label="Description">
        <TextArea {...register("description")} />
      </Field>
    </>
  );
}

export function DiscardAlert(props: {
  children: React.ReactNode;
  getIsDirty: () => boolean;
}) {
  const navigate = useNavigate();
  const onDiscard = () => navigate("/underwritingPrograms");
  return (
    <AlertDialog.Root>
      <AlertDialog.Trigger
        onClick={async e => {
          if (!props.getIsDirty()) {
            e.preventDefault();
            await onDiscard();
          }
        }}
      >
        {props.children}
      </AlertDialog.Trigger>
      <AlertDialog.Content>
        <VisuallyHidden>
          <AlertDialog.Title>Discard changes</AlertDialog.Title>
        </VisuallyHidden>
        <AlertDialog.Description size="2">
          Discard changes?
        </AlertDialog.Description>
        <Flex gap="3" mt="4" justify="end">
          <AlertDialog.Action>
            <Button color="red" onClick={onDiscard}>
              Discard
            </Button>
          </AlertDialog.Action>
          <AlertDialog.Cancel>
            <Button variant="soft" color="gray">
              Cancel
            </Button>
          </AlertDialog.Cancel>
        </Flex>
      </AlertDialog.Content>
    </AlertDialog.Root>
  );
}

export async function action(args: Route.ActionArgs) {
  const clerkAuth = await requireAuth(args);
  const { request } = args;

  const result = await parseFormData(
    request,
    editProgramSchema.refine(
      async values => {
        return !(await prisma.program.exists({
          name: values.name,
          id: { not: args.params.id },
          organization: { clerkId: clerkAuth.orgId },
        }));
      },
      { message: "A program with this name already exists.", path: ["name"] },
    ),
  );
  if (!result.success) {
    return data(
      { success: false, errors: result.error.format() },
      { status: 400 },
    );
  }
  await prisma.program.update({
    where: {
      id: args.params.id,
      organization: { clerkId: clerkAuth.orgId },
    },
    data: {
      name: result.data.name,
      descriptionText: result.data.description,
    },
  });
  return redirect("/underwritingPrograms");
}

export async function loader(args: Route.LoaderArgs) {
  const clerkAuth = await requireAuth(args);

  const program = await prisma.program.fetchWithCurrentVersionedRules(
    args.params.id ?? no(),
    { orgId: clerkAuth.orgId },
  );
  return safeData({ program });
}

function EditProgramPage({
  program,
}: {
  program: NonNullable<Route.ComponentProps["loaderData"]["program"]>;
}) {
  const fetcher = useFetcher<typeof action>();
  const form = useZodForm(editProgramSchema, {
    defaultValues: {
      name: program.name,
      description: program.descriptionText ?? "",
    },
    shouldUseNativeValidation: true,
  });
  useReportServerError(form, fetcher.data?.errors);

  const { defaultValues } = form.formState;
  const getIsDirty = () => !deepEqual(form.getValues(), defaultValues);
  return (
    <Flex gap="3" direction="column" mt="3">
      <Flex align="center" gap="3">
        <DiscardAlert getIsDirty={getIsDirty}>
          <IconButton>
            <ArrowLeft />
          </IconButton>
        </DiscardAlert>
        <Box>
          <Heading>Edit Program: {program.name}</Heading>
          <Heading size="3">
            {hv(program.descriptionText) ? (
              <>Description: {program.descriptionText}</>
            ) : (
              <em>(No description)</em>
            )}
          </Heading>
        </Box>
      </Flex>
      <form
        onSubmit={form.handleSubmit(async d => {
          await fetcher.submit(d, {
            method: "POST",
            encType: "application/json",
          });
        })}
      >
        <Flex direction="column" gap="3">
          <EditProgramFields register={form.register} />
          <Flex gap="2">
            <Button type="submit" loading={fetcher.state === "submitting"}>
              Submit Edit
            </Button>
            <DiscardAlert getIsDirty={getIsDirty}>
              <Button
                type="button"
                variant="outline"
                loading={fetcher.state === "submitting"}
              >
                Cancel
              </Button>
            </DiscardAlert>
          </Flex>
        </Flex>
      </form>
    </Flex>
  );
}

function ProgramNotFound() {
  return (
    <Flex gap="3" direction="column" mt="3">
      <Flex align="center" gap="3">
        <IconButton asChild>
          <Link to={`/underwritingPrograms`}>
            <ArrowLeft />
          </Link>
        </IconButton>
        <Box>
          <Heading>Edit Program</Heading>
        </Box>
      </Flex>
      Program not found
    </Flex>
  );
}

export default function WrappedPage({ loaderData }: Route.ComponentProps) {
  const { program } = loaderData;
  if (!hv(program)) {
    return <ProgramNotFound />;
  }
  return (
    <LoggedInWrapper>
      <EditProgramPage program={program} />
    </LoggedInWrapper>
  );
}
