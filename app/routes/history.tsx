import {
  <PERSON>,
  Card,
  Flex,
  Select,
  Spinner,
  Text,
  TextField,
} from "@radix-ui/themes";
import { useThrottle } from "@uidotdev/usehooks";
import { useCallback, useEffect } from "react";
import { data, useFetcher } from "react-router";
import { z } from "zod";
import {
  FinalRecommendationCard,
  recommendedActionUiInfo,
} from "~/components/FinalRecommendationCard";
import { LoggedInWrapper } from "~/components/LoggedInWrapper";
import { dedup, hv, useLastNonNull, useUzeroTitle } from "~/Tools";
import { requireAuth } from "~/utils/authHelpers.server";
import { fetchClerkUserMap } from "~/utils/clerkHelpers.server";
import { sortBy, sortByDesc } from "~/utils/Comparable";
import { prisma } from "~/utils/db.server";
import { whereAnalysisResultIsUserOrApi } from "~/utils/dbExtensions.server";
import { useField, useZodForm } from "~/utils/formHelpers";
import { safeData } from "~/utils/restHelpers";
import { parseFormData } from "~/utils/schemaHelpers.server";
import { zx } from "~/utils/validationHelpers";
import type { Route } from "./+types/history";

const searchHistorySchema = z.object({
  programId: zx.id().nullable(),
  searchText: zx.maybe(zx.saneString()),
});

export async function loader(args: Route.LoaderArgs) {
  const clerkAuth = await requireAuth(args);

  const programs = await prisma.program.findMany({
    where: { organization: { clerkId: clerkAuth.orgId } },
  });

  const dbAnalysisResults = await prisma.analysisResult.findMany({
    where: whereAnalysisResultIsUserOrApi(clerkAuth),
    include: { lineItems: true, versionedRules: true, creator: true },
  });

  const allRuleIds = dbAnalysisResults.flatMap(r =>
    r.versionedRules.map(r => r.id),
  );

  const versionedRules = await prisma.versionedRule.findMany({
    where: { id: { in: allRuleIds } },
  });

  return safeData({
    programs,
    analysisResults: dbAnalysisResults,
    versionedRules,
    clerkUserMap: await fetchClerkUserMap(dbAnalysisResults),
  });
}

export async function action(args: Route.ActionArgs) {
  const clerkAuth = await requireAuth(args);
  const { request } = args;

  const result = await parseFormData(request, searchHistorySchema);
  if (!result.success) {
    return data(
      { success: false as const, errors: result.error.format() },
      { status: 400 },
    );
  }
  const searchText = result.data.searchText;
  const recommendedActions = Object.entries(recommendedActionUiInfo)
    .filter(([, v]) =>
      v.uiName.toLowerCase().includes(searchText?.toLowerCase() ?? ""),
    )
    .map(([k]) => k);

  const analysisResults = await prisma.analysisResult.findMany({
    where: {
      AND: [
        whereAnalysisResultIsUserOrApi(clerkAuth),
        {
          programId: result.data.programId ?? undefined,
          OR: !hv(searchText)
            ? []
            : [
                {
                  recommendedAction: { in: recommendedActions },
                },
                {
                  overallAiReasoning: {
                    contains: searchText,
                    mode: "insensitive",
                  },
                },
                {
                  underwritingContent: {
                    contains: searchText,
                    mode: "insensitive",
                  },
                },
                {
                  lineItems: {
                    some: {
                      OR: [
                        {
                          reasoningText: {
                            contains: searchText,
                            mode: "insensitive",
                          },
                        },
                        {
                          textSegments: {
                            has: searchText,
                          },
                        },
                      ],
                    },
                  },
                },
              ],
        },
      ],
    },
    include: { lineItems: true, versionedRules: true, creator: true },
  });

  return safeData({
    success: true as const,
    analysisResults,
    versionedRules: dedup(
      analysisResults.flatMap(r => r.versionedRules),
      r => r.id,
    ),
    clerkUserMap: await fetchClerkUserMap(analysisResults),
  });
}

function ProgramsDropdown(props: {
  programs: Route.ComponentProps["loaderData"]["programs"];
  value: string | null;
  onChange: (value: string | null) => void;
}) {
  const allProgramsValue = "all";
  return (
    <Select.Root
      value={props.value ?? allProgramsValue}
      onValueChange={value =>
        props.onChange(value !== allProgramsValue ? value : null)
      }
    >
      <Select.Trigger />
      <Select.Content position="popper">
        <Select.Item value={allProgramsValue}>
          <strong>All Programs</strong>
        </Select.Item>
        {props.programs.toSorted(sortBy(p => p.name.toLowerCase())).map(p => (
          <Select.Item key={p.id} value={p.id}>
            {p.name}
          </Select.Item>
        ))}
      </Select.Content>
    </Select.Root>
  );
}

function SearchHistoryForm(props: {
  programs: Route.ComponentProps["loaderData"]["programs"];
  fetcher: ReturnType<typeof useFetcher<typeof action>>;
}) {
  const form = useZodForm(searchHistorySchema, {
    defaultValues: {
      programId: null,
      searchText: "",
    },
    shouldUseNativeValidation: true,
  });
  const { searchText, ...values } = form.watch();
  const throttledSearchText = useThrottle(searchText, 500);
  useEffect(() => {
    void props.fetcher.submit(
      { ...values, searchText: throttledSearchText },
      {
        method: "POST",
        encType: "application/json",
      },
    );
    // need to spread values here since it changes on every rerender
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [...Object.values(values), throttledSearchText]);
  const programIdField = useField(form.control, "programId");
  return (
    <>
      <Flex direction="column" gap="1" asChild>
        <label>
          <span>Program</span>
          <ProgramsDropdown programs={props.programs} {...programIdField} />
        </label>
      </Flex>
      <Flex direction="column" gap="1" asChild>
        <label>
          <span>Filter</span>
          <TextField.Root {...form.register("searchText")} required />
        </label>
      </Flex>
    </>
  );
}

type SuccessActionData = Route.ComponentProps["actionData"] & { success: true };
function SearchHistoryResults({
  data: actionData,
}: {
  data: SuccessActionData;
}) {
  const { analysisResults, versionedRules, clerkUserMap } = actionData;
  return (
    <>
      {analysisResults.length === 0 && (
        <Card>
          <Text asChild>
            <header>No history yet.</header>
          </Text>
        </Card>
      )}
      {analysisResults.toSorted(sortByDesc(r => r.createdAt)).map(r => (
        <FinalRecommendationCard
          key={r.id}
          result={r}
          unfilteredVersionedRules={versionedRules}
          clerkUserMap={clerkUserMap}
        />
      ))}
    </>
  );
}

function HistoryPage({ loaderData }: Route.ComponentProps) {
  useUzeroTitle("History");
  const fetcher = useFetcher<typeof action>();
  const { programs } = loaderData;
  const lastSuccessData = useLastNonNull(
    useCallback(
      () => (fetcher.data?.success === true ? fetcher.data : null),
      [fetcher.data],
    ),
  );
  return (
    <Flex direction="column" gap="3" my="3">
      <SearchHistoryForm programs={programs} fetcher={fetcher} />
      {!(fetcher.state === "idle" && fetcher.data?.success === true) && (
        <Box m="auto">
          <Spinner />
        </Box>
      )}
      {hv(lastSuccessData) && <SearchHistoryResults data={lastSuccessData} />}
    </Flex>
  );
}

export default function WrappedPage(props: Route.ComponentProps) {
  return (
    <LoggedInWrapper>
      <HistoryPage {...props} />
    </LoggedInWrapper>
  );
}
