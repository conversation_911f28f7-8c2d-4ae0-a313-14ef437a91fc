import { equipmentApplicationMd } from "~/utils/appSchemaConversionPrompt";
import { convertNatLangApplicationToJson } from "../utils/convert-schema.server";
import { EQUIPMENT_JSON_SCHEMA } from "~/EquipmentProgram/equipmentJsonSchema";

export async function action() {
  console.log("SUBMITTING for Schema Conversion...");
  const { errorsMd, applicationJson } = await convertNatLangApplicationToJson(
    equipmentApplicationMd,
    EQUIPMENT_JSON_SCHEMA,
  );
  console.log(errorsMd);
  console.log(applicationJson);

  // Response not getting typed as node's Response here
  return new Response("my response");
}
