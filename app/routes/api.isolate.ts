import {
  FullCodeValidationResult,
  getSchemaErrors,
  hv,
  logify,
  pretty,
} from "~/Tools";
import { SAMPLE_EQUIPMENT_APP } from "~/utils/ironConfig.server";

import {
  EQUIPMENT_JSON_SCHEMA,
  EquipmentApplication,
} from "~/EquipmentProgram/equipmentJsonSchema";
import { runIsolatedCode } from "~/utils/isolate.server";
import equipmentValidationFunctionsTs from "../EquipmentProgram/equipmentProgramVerificationFunctions.ts?raw";

export async function action() {
  // Feels best to validate this here, not in the VM:
  // - Simplifies and reduces code inside Sandbox
  // - We can tie schema/json/app together to ensure correct shape
  // - Avoids third-party code executing in VM
  const errors = getSchemaErrors(SAMPLE_EQUIPMENT_APP, EQUIPMENT_JSON_SCHEMA);
  if (hv(errors)) {
    throw new Error(pretty(errors));
  }

  // Just logs result
  const results = await codeValidateEquipmentApp(SAMPLE_EQUIPMENT_APP);

  console.log("=== RESULTS:");
  logify(results);

  return new Response("Done" as unknown as BodyInit);
}

async function codeValidateEquipmentApp(equipmentApp: EquipmentApplication) {
  console.log("ISOLATING code...");

  const result = await runIsolatedCode<FullCodeValidationResult>(
    equipmentValidationFunctionsTs,
    "validateEquipmentProgramApplication",
    equipmentApp,
  );

  console.log(
    `DONE with isolated code.  (isEverythingValid: ${result?.isEverythingValid})`,
  );

  return result;
}
