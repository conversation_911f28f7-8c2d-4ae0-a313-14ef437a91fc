import { type MetaFunction } from "react-router";
import { LoggedInWrapper } from "~/components/LoggedInWrapper";
import { MainNav } from "~/components/MainNav";

export const meta: MetaFunction = () => {
  return [
    { title: "UZero" },
    { name: "description", content: "Welcome to UZero" },
  ];
};

export default function Index() {
  return (
    <div className="flex min-h-screen">
      <div className="border-r bg-gray-100/40 dark:bg-gray-800/40 w-64 p-6">
        <div className="flex flex-col space-y-6">
          <div className="flex flex-col space-y-2">
            <h2 className="text-lg font-semibold">UAAS</h2>
            <MainNav className="flex flex-col space-y-1" />
          </div>
        </div>
      </div>
      <LoggedInWrapper>
        <h1 className="text-3xl font-bold">Welcome to UZero</h1>
      </LoggedInWrapper>
    </div>
  );
}
