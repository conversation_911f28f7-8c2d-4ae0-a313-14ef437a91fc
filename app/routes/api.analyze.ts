import { Prisma } from "@prisma/client";
import { data, LoaderFunction } from "react-router";
import {
  invokeModel,
  UnderwritingSchemaParsedType,
} from "~/llm/langchain-model.server";
import { SYSTEM_PROMPT_VERSION } from "~/llm/mainPrompt.server";
import { parseModelRawResult } from "~/llm/parseModelRawResult.server";
import { hv } from "~/Tools";
import { requireAuth } from "~/utils/authHelpers.server";
import { prisma } from "~/utils/db.server";
import { safeData } from "~/utils/restHelpers";
import { parseFormData } from "~/utils/schemaHelpers.server";
import { analyzeSchema } from "./analyzeHelpers";
import type { Route } from "./+types/api.analyze";
import { fetchClerkUserMap } from "~/utils/clerkHelpers.server";
import { SubmissionSource } from "prisma/interfaces";

export const action = async (args: Route.ActionArgs) => {
  console.log("in analyze");
  const clerkAuth = await requireAuth(args);

  const result = await parseFormData(args.request, analyzeSchema);

  if (!result.success) {
    return data(
      { success: false as const, errors: result.error },
      { status: 400 },
    );
  }

  const program = await prisma.program.fetchWithCurrentVersionedRulesOrThrow(
    result.data.programId,
    { orgId: clerkAuth.orgId },
  );

  const rawModelResult = await invokeModel(
    program,
    result.data.underwritingContent,
  );
  console.log(JSON.stringify(rawModelResult, null, 2));

  const analyzeResult = parseModelRawResult(rawModelResult);
  console.log(JSON.stringify(analyzeResult, null, 2));

  if (
    analyzeResult.recommendedAction !==
    rawModelResult.finalRecommendation.recommendedAction
  ) {
    // Send / log dev message here
    console.log("\n***** LLM/RULES EVALUATION MISMATCH *****");
  }

  const lineItemInputs = getAnalysisLineItemInputs({
    breakdown: rawModelResult,
  });

  const dbResult = await prisma.analysisResult.create({
    data: {
      programId: program.id,
      programName: program.name,
      programDescription: program.descriptionText,
      recommendedAction: analyzeResult.recommendedAction,
      systemPromptVersion: SYSTEM_PROMPT_VERSION,
      underwritingContent: result.data.underwritingContent,
      developerMessage: rawModelResult.internalDeveloperMessage,
      overallConfidencePct: analyzeResult.confidencePct,
      overallAiReasoning: analyzeResult.aiReasoning,
      versionedRules: {
        connect: program.currentVersionedRules.map(r => ({ id: r.id })),
      },
      creator: { connect: { clerkId: clerkAuth.userId } },
      submissionSource: SubmissionSource.USER,
      lineItems: {
        createMany: { data: [...lineItemInputs] },
      },
    },
    include: { lineItems: true, creator: true, versionedRules: true },
  });

  return safeData({
    success: true as const,
    analysisResult: dbResult,
    versionedRules: dbResult.versionedRules,
    clerkUserMap: await fetchClerkUserMap(dbResult),
  });
};

export type AnalyzeActionFn = typeof action;

function* getAnalysisLineItemInputs({
  breakdown,
}: {
  breakdown: UnderwritingSchemaParsedType;
}): Generator<Prisma.AnalysisLineItemCreateManyAnalysisResultInput> {
  if (hv(breakdown.adversarialContent)) {
    yield {
      confidencePct: breakdown.adversarialContent.confidencePercent,
      contentType: "ADVERSARIAL",
      reasoningText: breakdown.adversarialContent.reasoningText,
    };
  }

  for (const m of breakdown.ruleMatches ?? []) {
    yield {
      confidencePct: m.confidencePercent,
      riskPct: m.additionalRiskPercent,
      contentType: "RULE_MATCH",
      reasoningText: m.reasoningText,
      textSegments: m.matchingTextSegments,
      versionedRuleId: m.ruleId,
    };
  }

  for (const c of breakdown.unmatchedCoverageContent ?? []) {
    yield {
      confidencePct: c.confidencePercent,
      contentType: "UNMATCHED_RISK",
      reasoningText: c.reasoningText,
      textSegments: c.matchingTextSegments,
      riskPct: c.riskPercent,
    };
  }

  for (const c of breakdown.inscrutableAndOtherContent ?? []) {
    yield {
      confidencePct: c.confidencePercent,
      contentType: "INSCRUTABLE",
      reasoningText: c.reasoningText,
      textSegments: c.matchingTextSegments,
    };
  }

  if (hv(breakdown.connectiveText)) {
    yield {
      confidencePct: -1,
      reasoningText: "No reasoning for this type",
      contentType: "CONNECTIVE",
      textSegments: breakdown.connectiveText,
    };
  }
}

export const loader: LoaderFunction = () => {
  throw new Response("Method Not Allowed", { status: 405 });
};
