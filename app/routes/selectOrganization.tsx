import { useOrganizationList } from "@clerk/react-router";
import { Flex, Spinner } from "@radix-ui/themes";
import { useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router";
import { hv } from "~/Tools";

export default function SelectOrganizationPage() {
  const [searchParams] = useSearchParams();
  const redirectTo = searchParams.get("redirectTo") ?? "/";

  const navigate = useNavigate();
  // can only set active org client-side
  // https://clerk.com/docs/organizations/force-organizations#set-an-active-organization
  const orgList = useOrganizationList({ userMemberships: true });
  useEffect(() => {
    if (!orgList.isLoaded) {
      return;
    }
    const firstOrg = orgList.userMemberships.data[0];
    if (!hv(firstOrg)) {
      return;
    }
    void orgList
      .setActive({ organization: firstOrg.organization })
      .then(() => navigate(redirectTo));
    // everything not listed here either shouldn't change, or changes on every rerender
    // this use case will be resolved by useEffectEvent in a future react version
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orgList.isLoaded, orgList.userMemberships.data, orgList.setActive]);
  return (
    <Flex px="5" py="7" justify="center">
      <Spinner />
    </Flex>
  );
}
