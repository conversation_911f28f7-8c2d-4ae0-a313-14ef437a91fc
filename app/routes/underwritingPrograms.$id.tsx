import {
  FlatMultiplierType,
  PercentMultiplierType,
  RuleCategory,
} from "prisma/interfaces";
import {
  AlertDialog,
  Badge,
  Box,
  Button,
  Card,
  Dialog,
  Flex,
  Heading,
  IconButton,
  Text,
  TextArea,
} from "@radix-ui/themes";
import { data, Link, useFetcher } from "react-router";

import { ArrowLeft, Edit, Plus, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { z } from "zod";
import { FinalRecommendationCard } from "~/components/FinalRecommendationCard";
import { ErrorText } from "~/components/HelperComponents";
import { LoggedInWrapper } from "~/components/LoggedInWrapper";
import { RuleForm, ruleFormSchema } from "~/components/RuleForm";
import {
  CategoryBadgeColor,
  flatMultplierTypeInfo,
  percentMultiplierTypeInfo,
  RuleSortMap,
  RuleUiStrings,
} from "~/Constants";
import {
  Assert,
  excludeEnum,
  formatBasisPoints,
  formatDollarsAndOptionalCents,
  hv,
  no,
  useCounterKey,
} from "~/Tools";
import { requireAuth } from "~/utils/authHelpers.server";
import { inSequence, sortBy } from "~/utils/Comparable";
import { prisma } from "~/utils/db.server";
import { useZodForm } from "~/utils/formHelpers";
import { safeData } from "~/utils/restHelpers";
import { parseFormData } from "~/utils/schemaHelpers.server";
import { zx } from "~/utils/validationHelpers";
import { useAnalyzeFetcher } from "../lib/analysisHelpers";
import type { Route } from "./+types/underwritingPrograms.$id";
import { analyzeSchema } from "./analyzeHelpers";

type DeleteAlertProps = {
  onDelete: () => void;
  children: React.ReactNode;
  rulePreview: string;
};

const DeleteAlert = ({ onDelete, children, rulePreview }: DeleteAlertProps) => {
  const truncatedPreview =
    rulePreview.length > 100 ? `${rulePreview.slice(0, 97)}...` : rulePreview;

  return (
    <AlertDialog.Root>
      <AlertDialog.Trigger>{children}</AlertDialog.Trigger>
      <AlertDialog.Content>
        <AlertDialog.Title>Delete Rule</AlertDialog.Title>
        <AlertDialog.Description size="2">
          Are you sure you want to delete this rule?
          <Card my="2" asChild>
            <span>
              <Text size="2">&quot;{truncatedPreview}&quot;</Text>
            </span>
          </Card>
          This action cannot be undone.
        </AlertDialog.Description>

        <Flex gap="3" mt="4" justify="end">
          <AlertDialog.Action>
            <Button color="red" onClick={onDelete}>
              Delete
            </Button>
          </AlertDialog.Action>
          <AlertDialog.Cancel>
            <Button variant="soft" color="gray">
              Cancel
            </Button>
          </AlertDialog.Cancel>
        </Flex>
      </AlertDialog.Content>
    </AlertDialog.Root>
  );
};

export const createRuleSchema = z.intersection(
  z.object({
    text: zx.saneString(),
    basisValue: z.coerce.number().min(0).default(0),
  }),
  z.union([
    z.object({
      category: z.literal(RuleCategory.FLAT_PREMIUM),
      flatMultiplierType: z.nativeEnum(FlatMultiplierType),
    }),
    z.object({
      category: z.literal(RuleCategory.PERCENT_PREMIUM),
      percentMultiplierType: z.nativeEnum(PercentMultiplierType),
    }),
    z.object({
      category: z.enum(
        excludeEnum(RuleCategory, [
          RuleCategory.FLAT_PREMIUM,
          RuleCategory.PERCENT_PREMIUM,
        ]),
      ),
    }),
  ]),
);

const ruleMetadataIdSchema = z.object({
  ruleMetadataId: zx.id(),
});

export const updateRuleSchema = z.intersection(
  createRuleSchema,
  ruleMetadataIdSchema,
);

export const deleteRuleSchema = ruleMetadataIdSchema;

export async function action(args: Route.ActionArgs) {
  const clerkAuth = await requireAuth(args);
  const { request } = args;

  if (request.method === "POST") {
    const result = await parseFormData(request, createRuleSchema);
    if (!result.success) {
      return data({ success: false, errors: result.error }, { status: 400 });
    }
    await prisma.ruleMetadata.create({
      data: {
        program: {
          connect: {
            id: args.params.id,
            organization: { clerkId: clerkAuth.orgId },
          },
        },
        versions: {
          create: {
            ...result.data,
            creator: { connect: { clerkId: clerkAuth.userId } },
          },
        },
      },
    });
    return { success: true };
  } else if (request.method === "PUT") {
    const result = await parseFormData(request, updateRuleSchema);
    if (!result.success) {
      return data({ success: false, errors: result.error }, { status: 400 });
    }
    const { ruleMetadataId, ...ruleData } = result.data;
    await prisma.ruleMetadata.update({
      where: {
        id: ruleMetadataId,
        program: {
          id: args.params.id,
          organization: { clerkId: clerkAuth.orgId },
        },
      },
      data: {
        versions: {
          create: {
            ...ruleData,
            creator: { connect: { clerkId: clerkAuth.userId } },
          },
        },
      },
    });
    return { success: true };
  } else if (request.method === "DELETE") {
    const result = await parseFormData(request, deleteRuleSchema);
    if (!result.success) {
      return data({ success: false, errors: result.error }, { status: 400 });
    }
    await prisma.ruleMetadata.update({
      where: {
        id: result.data.ruleMetadataId,
        program: {
          id: args.params.id,
          organization: { clerkId: clerkAuth.orgId },
        },
      },
      data: { deletedAt: new Date() },
    });
    return { success: true };
  }
  return data({ success: false }, { status: 400 });
}

type RuleItemProps = {
  rule: Route.ComponentProps["loaderData"]["program"]["currentVersionedRules"][0];
};

export function RuleCard({
  rule,
  isReadonly = false,
}: RuleItemProps & { isReadonly?: boolean }) {
  const fetcher = useFetcher<typeof action>();

  const handleDelete = async () => {
    await fetcher.submit(
      { ruleMetadataId: rule.ruleMetadataId },
      { method: "DELETE", encType: "application/json" },
    );
  };

  return (
    <Card size="2" className="group relative">
      <Flex direction="row" gap="2" justify="between" align="center">
        <Flex direction="column" gap="2" style={{ flex: 1 }}>
          <Flex justify="between" align="center">
            <Badge color={CategoryBadgeColor[rule.category]}>
              {RuleUiStrings[rule.category]}
            </Badge>
            <BasisText rule={rule} />
          </Flex>
          <Text size="2">{rule.text}</Text>
        </Flex>
        {!isReadonly && (
          <Flex
            direction="row"
            gap="3"
            className="invisible group-hover:visible"
          >
            <EditRuleButton rule={rule} />
            <DeleteAlert
              onDelete={() => handleDelete()}
              rulePreview={rule.text}
            >
              <IconButton variant="ghost" color="red" highContrast>
                <Trash2 width="16" height="16" />
              </IconButton>
            </DeleteAlert>
          </Flex>
        )}
      </Flex>
    </Card>
  );
}

function BasisText({ rule }: RuleItemProps) {
  let basisText = "";
  switch (rule.category) {
    case "EXCLUDE":
    case "INCLUDE":
    case "REQUIRES_UNDERWRITER_REVIEW":
      return null;

    case "FLAT_PREMIUM": {
      const flatMultiplierInfo =
        flatMultplierTypeInfo[rule.flatMultiplierType ?? no()];
      basisText = `${formatDollarsAndOptionalCents(rule.basisValue)} ${
        flatMultiplierInfo.uiString
      }`;
      break;
    }
    case "PERCENT_PREMIUM": {
      const percentMultiplierInfo =
        percentMultiplierTypeInfo[rule.percentMultiplierType ?? no()];
      basisText = `${formatBasisPoints(rule.basisValue)} of ${
        percentMultiplierInfo.uiString
      }`;
      break;
    }
    default:
      Assert.hard(false, `Unrecognized rule category: ${rule.category}`);
  }
  return (
    <Text size="1" color="gray">
      {basisText}
    </Text>
  );
}

export async function loader(args: Route.LoaderArgs) {
  const clerkAuth = await requireAuth(args);

  const program = await prisma.program.fetchWithCurrentVersionedRulesOrThrow(
    args.params.id ?? no(),
    { orgId: clerkAuth.orgId },
  );
  return safeData({ program });
}

function NewRuleButton() {
  const fetcher = useFetcher<typeof action>();
  const [dialogOpen, setDialogOpen] = useState(false);
  useEffect(() => {
    if (fetcher.state === "idle" && (fetcher.data?.success ?? false)) {
      setDialogOpen(false);
    }
  }, [fetcher.state, fetcher.data]);
  return (
    <Dialog.Root open={dialogOpen} onOpenChange={setDialogOpen}>
      <Dialog.Description>{null /*required*/}</Dialog.Description>
      <Dialog.Trigger>
        <Button>
          <Plus /> New Rule
        </Button>
      </Dialog.Trigger>
      <Dialog.Content>
        <Dialog.Title>Create New Rule</Dialog.Title>
        <RuleForm
          loading={fetcher.state === "submitting"}
          onCancel={() => setDialogOpen(false)}
          onSubmit={async data => {
            await fetcher.submit(data, {
              method: "POST",
              encType: "application/json",
            });
          }}
          submitText="Add Rule"
        />
      </Dialog.Content>
    </Dialog.Root>
  );
}

function EditRuleButton(props: { rule: RuleItemProps["rule"] }) {
  const fetcher = useFetcher<typeof action>();
  const [dialogOpen, setDialogOpen] = useState(false);
  useEffect(() => {
    if (fetcher.state === "idle" && (fetcher.data?.success ?? false)) {
      setDialogOpen(false);
    }
  }, [fetcher.state, fetcher.data]);
  return (
    <Dialog.Root open={dialogOpen} onOpenChange={setDialogOpen}>
      <Dialog.Trigger>
        <IconButton variant="ghost" highContrast>
          <Edit width="16" height="16" />
        </IconButton>
      </Dialog.Trigger>
      <Dialog.Content>
        <Dialog.Title>Edit Rule</Dialog.Title>
        <RuleForm
          value={props.rule as z.input<typeof ruleFormSchema>}
          loading={fetcher.state === "submitting"}
          onCancel={() => setDialogOpen(false)}
          onSubmit={async data => {
            await fetcher.submit(
              { ruleMetadataId: props.rule.ruleMetadataId, ...data },
              { method: "PUT", encType: "application/json" },
            );
          }}
          submitText="Submit"
        />
      </Dialog.Content>
    </Dialog.Root>
  );
}

function ProgramPage({ loaderData }: Route.ComponentProps) {
  const { program } = loaderData;
  const analyzeFetcher = useAnalyzeFetcher();
  const analyzeIsSubmitting = analyzeFetcher.state === "submitting";
  const [recommendationKey, rotateRecommendationKey] = useCounterKey();

  const sortedRules = program.currentVersionedRules.toSorted(
    sortBy(r =>
      inSequence(RuleSortMap[r.category], r.text.toLocaleLowerCase()),
    ),
  );

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useZodForm(analyzeSchema, { defaultValues: { programId: program.id } });

  return (
    <Flex direction="column" gap="3" mt="3">
      <Flex align="center" gap="3">
        <IconButton asChild>
          <Link to="/underwritingPrograms">
            <ArrowLeft />
          </Link>
        </IconButton>
        <Box flexGrow="1">
          <Heading>Program: {program.name}</Heading>
          <Heading size="3">
            {hv(program.descriptionText) ? (
              <>Description: {program.descriptionText}</>
            ) : (
              <em>(No description)</em>
            )}
          </Heading>
        </Box>
        <Link
          style={{ color: "var(--accent-9)", fontSize: ".9em" }}
          to={`/editProgram/${program.id}`}
        >
          Edit Program Details
        </Link>
      </Flex>
      <Box>
        <form
          onSubmit={handleSubmit(async d => {
            rotateRecommendationKey();
            await analyzeFetcher.submit(d, {
              method: "POST",
              action: "/api/analyze",
              encType: "application/json",
            });
          })}
        >
          <Box>
            <Text as="label" size="3" htmlFor="underwritingContent">
              Underwriting Content
            </Text>
            <TextArea
              {...register("underwritingContent")}
              placeholder="Enter underwriting content..."
              rows={8}
              mt="1"
            />
            <ErrorText fieldError={errors.underwritingContent} />
          </Box>
          <Flex justify="end" mt="2">
            <Button type="submit" disabled={analyzeIsSubmitting}>
              {analyzeIsSubmitting ? "Analyzing..." : "Submit"}
            </Button>
          </Flex>
        </form>
      </Box>
      {hv(analyzeFetcher.data) && !analyzeIsSubmitting && (
        <>
          {!analyzeFetcher.data.success ? (
            <Text color="red">
              Error:{" "}
              {Object.values(analyzeFetcher.data.errors ?? no())
                .flatMap(v => v ?? [])
                .join("; ")}
            </Text>
          ) : (
            <FinalRecommendationCard
              key={recommendationKey}
              result={analyzeFetcher.data.analysisResult}
              unfilteredVersionedRules={analyzeFetcher.data.versionedRules}
              clerkUserMap={analyzeFetcher.data.clerkUserMap}
            />
          )}
        </>
      )}
      <Flex justify="between" align="center" pt="4">
        <Box>
          <Heading>Rules</Heading>
        </Box>
        <NewRuleButton />
      </Flex>
      {sortedRules.length === 0 ? (
        <div className="mt-12 text-center">
          <h3 className="mt-2 text-sm font-medium text-white">No rules</h3>
          <p className="mt-1 text-sm text-neutral-400">
            No rules have been configured yet.
          </p>
        </div>
      ) : (
        <Flex direction="column" gap="3" width="auto">
          {sortedRules.map(rule => (
            <RuleCard key={rule.id} rule={rule} />
          ))}
        </Flex>
      )}
    </Flex>
  );
}

export default function WrappedPage(props: Route.ComponentProps) {
  return (
    <LoggedInWrapper>
      <ProgramPage {...props} />
    </LoggedInWrapper>
  );
}
