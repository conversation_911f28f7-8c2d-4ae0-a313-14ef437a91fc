import {
  Box,
  Button,
  Card,
  <PERSON>lex,
  Heading,
  IconButton,
  Text,
} from "@radix-ui/themes";
import { Edit, Plus } from "lucide-react";
import { Link } from "react-router";
import { LoggedInWrapper } from "~/components/LoggedInWrapper";
import { requireAuth } from "~/utils/authHelpers.server";
import { prisma } from "~/utils/db.server";
import { safeData } from "~/utils/restHelpers";
import type { Route } from "./+types/underwritingPrograms._index";
import { useUzeroTitle } from "~/Tools";

export async function loader(args: Route.LoaderArgs) {
  const clerkAuth = await requireAuth(args);

  const programs = await prisma.program.findMany({
    where: { organization: { clerkId: clerkAuth.orgId } },
    orderBy: { createdAt: "desc" },
  });
  return safeData({ programs });
}

function ProgramCard({
  program,
}: {
  program: Route.ComponentProps["loaderData"]["programs"][0];
}) {
  return (
    <Card key={program.id} className="group relative">
      <Flex direction="row" gap="2" align="center">
        <Box flexGrow="1" asChild>
          <Link to={`/underwritingPrograms/${program.id}`}>
            <Box>
              <Text size="5">{program.name}</Text>
            </Box>
            <Box>{program.descriptionText ?? <em>(No description)</em>}</Box>
          </Link>
        </Box>
        <Flex direction="row" gap="3" className="invisible group-hover:visible">
          <IconButton variant="ghost" asChild>
            <Link to={`/editProgram/${program.id}`}>
              <Edit width="16" height="16" />
            </Link>
          </IconButton>
        </Flex>
      </Flex>
    </Card>
  );
}

function ProgramsPage({ loaderData }: Route.ComponentProps) {
  useUzeroTitle("Programs");

  const { programs } = loaderData;
  return (
    <Flex gap="3" direction="column" mt="3">
      <Flex justify="between">
        <Heading>Programs</Heading>
        <Button asChild>
          <Link to="/createProgram">
            <Plus /> Create New Program
          </Link>
        </Button>
      </Flex>
      {programs.length === 0 ? (
        <Flex justify="center">
          <em>No programs yet.</em>
        </Flex>
      ) : (
        programs.map(p => <ProgramCard key={p.id} program={p} />)
      )}
    </Flex>
  );
}

export default function WrappedPage(props: Route.ComponentProps) {
  return (
    <LoggedInWrapper>
      <ProgramsPage {...props} />
    </LoggedInWrapper>
  );
}
