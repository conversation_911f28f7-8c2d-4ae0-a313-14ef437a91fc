import Anthropic from "@anthropic-ai/sdk";
import { Tool } from "@anthropic-ai/sdk/resources/index.mjs";
import { Button } from "@radix-ui/themes";
import { envSecret } from "~/env.server";
import {
  CLAUDE_LATEST_MODEL,
  sonnetMaxOutputTokens,
} from "~/llm/anthropic.server";
import { Ajv, Assert, hv, logify, no, pretty } from "~/Tools";
import extendedJsonSchemaSchema from "~/utils/extended-json-schema-schema.json";
import { safeData } from "~/utils/restHelpers";
import { readJsonSchemaToolPrompt } from "~/utils/systemPrompts";
import type { Route } from "../+types/root";

const userPrompt = /*md*/ `
- **Coverage Start Date**
  - Future date, at most 30 days from today (including today)
- **Date of Birth**
  - Validation: Must be at least 18 years old on the coverage start date
- **Date of business incorporation** (must be a specific past date, anytime in past)
- **Equipment Coverage Amounts**:
  - **Owned Equipment Coverage**
  - **Rented Equipment Coverage**
  - **Rental Reimbursement Coverage**

Coverage amount validations:

- Total combined coverage cannot exceed $1,000,000
- The Owned amount can only be up to 2x the Rented amount.

## Outputs (dollars and cents)

- **Base Premium**: 5% of total coverage amount
- **Admin Fee**: $25.00
- **Total Cost**: Base Premium + Admin Fee
- Calculate different total premium: sum total coverage amount above, premium is 5% of that total, and prorate from coverage start date till end of year (e.g. for a policy that always renews Jan 1).
`;

export async function action(_: Route.LoaderArgs) {
  const useThinking = false;

  const anthropicClient = new Anthropic({
    apiKey: envSecret.ANTHROPIC_API_KEY,
  });

  const jsonResult = useThinking
    ? await createSchemaWithThinking(anthropicClient)
    : await createSchema(anthropicClient);
  console.log(jsonResult.schema);
  // claude will generate this as a string if it doesn't match the schema exactly
  // parse it here so ajv can give more detailed errors
  jsonResult.schema =
    typeof jsonResult.schema === "string"
      ? JSON.parse(jsonResult.schema)
      : jsonResult.schema;
  const validator = Ajv.compile(extendedJsonSchemaSchema);
  // usually fails if system prompt doesn't match schema in some way
  validator(jsonResult);
  console.log(validator.errors);
  return safeData(jsonResult.schema);

  async function createSchema(client: Anthropic) {
    const message = await client.messages.create({
      model: CLAUDE_LATEST_MODEL,
      max_tokens: sonnetMaxOutputTokens,
      temperature: 0,
      tools: [
        {
          input_schema: extendedJsonSchemaSchema as Tool.InputSchema,
          name: "submitJsonSchema",
        },
      ],
      tool_choice: { name: "submitJsonSchema", type: "tool" },
      system: [
        {
          text: await readJsonSchemaToolPrompt(),
          type: "text",
          cache_control: { type: "ephemeral" },
        },
      ],
      messages: [{ role: "user", content: userPrompt }],
    });
    logify(message);
    const messageContent = message.content.find(m => m.type === "tool_use");
    Assert.hard(hv(messageContent), "invalid Anthropic return");
    return messageContent.input as { schema: unknown };
  }

  async function createSchemaWithThinking(client: Anthropic) {
    const message = await client.messages.create({
      model: CLAUDE_LATEST_MODEL,
      max_tokens: 20000,
      thinking: {
        type: "enabled",
        budget_tokens: 16000,
      },
      system: [
        {
          text: await readJsonSchemaToolPrompt(),
          type: "text",
          cache_control: { type: "ephemeral" },
        },
      ],
      messages: [{ role: "user", content: userPrompt }],
    });
    logify(message);
    const messageContent = message.content.find(m => m.type === "text");
    Assert.hard(hv(messageContent), "invalid Anthropic return");
    const jsonContent =
      /```(?:json)?(.*?)```/s.exec(messageContent.text)?.[1] ?? no();
    return JSON.parse(jsonContent) as { schema: unknown };
  }
}

export default function Page({ actionData }: Route.ComponentProps) {
  return (
    <form method="post">
      <pre>{pretty(actionData)}</pre>
      <Button>Submit</Button>
    </form>
  );
}
