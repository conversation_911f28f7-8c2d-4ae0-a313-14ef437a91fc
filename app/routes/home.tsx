import { <PERSON>, <PERSON><PERSON>, Card, Flex, Heading, Text } from "@radix-ui/themes";
import { <PERSON> } from "react-router";
import { FinalRecommendationCard } from "~/components/FinalRecommendationCard";
import { LoggedInWrapper } from "~/components/LoggedInWrapper";
import { dedup, useUzeroTitle } from "~/Tools";
import { requireAuth } from "~/utils/authHelpers.server";
import { fetchClerkUserMap } from "~/utils/clerkHelpers.server";
import { prisma } from "~/utils/db.server";
import { whereAnalysisResultIsUserOrApi } from "~/utils/dbExtensions.server";
import { safeData } from "~/utils/restHelpers";
import type { Route } from "./+types/home";

export async function loader(args: Route.LoaderArgs) {
  const clerkAuth = await requireAuth(args);

  const latestPrograms = await prisma.program.findMany({
    where: { organization: { clerkId: clerkAuth.orgId } },
    orderBy: { createdAt: "desc" },
    take: 3,
  });

  const latestAnalysisResults = await prisma.analysisResult.findMany({
    where: whereAnalysisResultIsUserOrApi(clerkAuth),
    include: { lineItems: true, versionedRules: true, creator: true },
    orderBy: { createdAt: "desc" },
    take: 3,
  });

  return safeData({
    latestPrograms,
    latestAnalysisResults,
    versionedRules: dedup(
      latestAnalysisResults.flatMap(r => r.versionedRules),
      r => r.id,
    ),
    clerkUserMap: await fetchClerkUserMap(latestAnalysisResults),
  });
}

function HomePage({ loaderData }: Route.ComponentProps) {
  useUzeroTitle("Home");
  const {
    latestPrograms,
    latestAnalysisResults,
    versionedRules,
    clerkUserMap,
  } = loaderData;
  return (
    <Flex
      direction={{ initial: "column", sm: "row" }}
      align={{ sm: "start" }}
      gap="3"
      mt="3"
    >
      <Box flexBasis={{ sm: "0" }} asChild>
        <Card style={{ flexGrow: 1 }}>
          <Flex gap="3" direction="column">
            <Heading>Programs</Heading>
            {latestPrograms.length === 0 ? (
              <Flex justify="center">
                <Button asChild>
                  <Link to="/createProgram">Create a program</Link>
                </Button>
              </Flex>
            ) : (
              <>
                {latestPrograms.map(p => (
                  <Card key={p.id} asChild>
                    <Link to={`/underwritingPrograms/${p.id}`}>
                      <Box>
                        <Text size="5">{p.name}</Text>
                      </Box>
                      <Box>
                        {p.descriptionText ?? <em>(No description)</em>}
                      </Box>
                    </Link>
                  </Card>
                ))}
                <Flex justify="center" gap="3">
                  <Button asChild>
                    <Link to="/underwritingPrograms">Show All</Link>
                  </Button>
                </Flex>
              </>
            )}
          </Flex>
        </Card>
      </Box>
      <Box flexBasis={{ sm: "0" }} asChild>
        <Card style={{ flexGrow: 1 }}>
          <Flex gap="3" direction="column">
            <Heading>History</Heading>
            {latestAnalysisResults.length === 0 ? (
              <Card>No submissions yet.</Card>
            ) : (
              <>
                <Flex direction="column" gap="3" my="3">
                  {latestAnalysisResults.map(r => (
                    <FinalRecommendationCard
                      key={r.id}
                      result={r}
                      unfilteredVersionedRules={versionedRules}
                      clerkUserMap={clerkUserMap}
                    />
                  ))}
                </Flex>
                <Flex justify="center">
                  <Button asChild>
                    <Link to="/history">Show All</Link>
                  </Button>
                </Flex>
              </>
            )}
          </Flex>
        </Card>
      </Box>
      <Box flexBasis={{ sm: "0" }} asChild>
        <Card style={{ flexGrow: 1 }}>
          <Flex gap="3" direction="column">
            <Heading>Debug Functions</Heading>
            <Button
              onClick={async () => {
                await fetch("/api/convert-schema", {
                  method: "POST",
                });
              }}
            >
              Convert Schema
            </Button>

            <Button
              onClick={async () => {
                await fetch("/api/iron", {
                  method: "POST",
                });
              }}
            >
              Run Iron
            </Button>

            <Button
              onClick={async () => {
                await fetch("/api/isolate", {
                  method: "POST",
                });
              }}
            >
              Isolate
            </Button>
          </Flex>
        </Card>
      </Box>
    </Flex>
  );
}

export default function WrappedPage(props: Route.ComponentProps) {
  return (
    <LoggedInWrapper>
      <HomePage {...props} />
    </LoggedInWrapper>
  );
}
