import * as crypto from "crypto";
import { envSecret } from "~/env.server";

export function signMessage(message: string) {
  return crypto
    .createHmac("sha1", envSecret.ANTHROPIC_API_KEY)
    .update(message)
    .digest("base64url");
}

export function verifyMessage(message: string, expectedSignature: string) {
  const signature = signMessage(message);
  return (
    signature.length === expectedSignature.length &&
    crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature),
    )
  );
}
