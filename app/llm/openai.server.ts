import { envSecret } from "~/env.server";
import { underwritingSchema } from "./mainPrompt.server";
import { no } from "~/Tools";
import { OpenAI } from "openai";
import { zodResponseFormat } from "openai/helpers/zod";

const client = new OpenAI({
  apiKey: envSecret.OPENAI_API_KEY, // This is the default and can be omitted
});

export async function invokeOpenaiModel({
  systemPrompt,
  userPrompt,
}: {
  userPrompt: string;
  systemPrompt: string;
}) {
  // Prompting is automatic for reused "prefixes" in openai api
  const completionResult = await client.chat.completions.create({
    messages: [
      {
        role: "system",
        content: systemPrompt,
      },
      { role: "user", content: userPrompt },
    ],
    // response_format is recommended over JSON mode (stronger guarantees)
    response_format: zodResponseFormat(underwritingSchema, "underwriting"),
    model: "gpt-4o",
  });

  console.log(completionResult);
  const message = completionResult.choices[0]?.message;
  console.log(message);
  return underwritingSchema.parse(JSON.parse(message?.content ?? no()));
}
