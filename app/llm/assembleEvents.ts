import type { ContentBlockParam } from "@anthropic-ai/sdk/resources/messages.mjs";
import { Assert, hv, no, typecheckAssign } from "~/Tools";
import { CustomEvent, MessageWithId, StreamEvent } from "./streamEvent";

export type AssembledEvent = {
  event: StreamEvent;
  message: MessageWithId;
  contentBlock?: ContentBlockParam | undefined;
  messages: MessageWithId[];
};

function deepCopy<T>(obj: T): T {
  return JSON.parse(JSON.stringify(obj));
}

const expectedContentBlockTypes: ContentBlockParam["type"][] = [
  "text",
  "tool_use",
  "tool_result",
  "server_tool_use",
  "web_search_tool_result",
];
export function isExpectedContentBlock(contentBlock: ContentBlockParam) {
  if (expectedContentBlockTypes.includes(contentBlock.type)) {
    return true;
  } else {
    return false;
  }
}

export async function* assembleEvents(
  events: AsyncGenerator<StreamEvent>,
  originalMessages: MessageWithId[] = [],
): AsyncGenerator<AssembledEvent> {
  const messages = [...originalMessages];
  let message:
    | (MessageWithId & { content: { partial_input_json?: string }[] })
    | undefined = undefined;
  for await (const rawEvent of events) {
    // this function is mutating for now, otherwise it would require a lot of memory
    const event = deepCopy(rawEvent);
    if (event.type === "message_start") {
      message = {
        id: event.message.id,
        role: event.message.role,
        content: event.message.content,
      };
    }
    Assert.hard(
      hv(message),
      `received ${event.type} with no prior message_start`,
    );
    if (event.type === "message_start") {
      messages.push(message);
      yield { event, message, messages };
    } else if (event.type === "message_delta") {
      Object.assign(message, event.delta);
      if (
        message?.stop_reason !== "end_turn" &&
        message?.stop_reason != "tool_use"
      ) {
        console.warn(
          `[Message Stop] Non-end_turn stop reason: ${message?.stop_reason}`,
        );
      }
      yield { event, message, messages };
    } else if (event.type === "message_stop") {
      yield { event, message, messages };
    } else if (event.type === "content_block_start") {
      message.content[event.index] = event.content_block;
      yield { event, message, contentBlock: event.content_block, messages };
    } else if (event.type === "content_block_delta") {
      const contentBlock = message.content[event.index] ?? no();
      if (event.delta.type === "text_delta" && "text" in contentBlock) {
        contentBlock.text += event.delta.text;
        yield { event, message, contentBlock, messages };
      } else if (event.delta.type === "input_json_delta") {
        contentBlock.partial_input_json ??= "";
        contentBlock.partial_input_json += event.delta.partial_json;
        yield { event, message, contentBlock, messages };
      }
    } else if (event.type === "content_block_stop") {
      const contentBlock = message.content[event.index] ?? no();
      if (hv(contentBlock.partial_input_json) && "input" in contentBlock) {
        try {
          contentBlock.input = JSON.parse(contentBlock.partial_input_json);
          delete contentBlock.partial_input_json;
        } catch (e: unknown) {
          const errorMessage = e instanceof Error ? e.message : String(e);
          console.error(
            `[ToolCall Error] Failed to parse JSON for tool ${contentBlock.name}: ${errorMessage}`,
          );
        }
      } else if (!isExpectedContentBlock(contentBlock)) {
        // We can't splice it out of the array, because the event indices depend on the array position
        console.error(
          `[Content Block] Unexpected content block: ${contentBlock}`,
        );
      }
      yield { event, message, contentBlock, messages };
    } else {
      typecheckAssign<typeof event, CustomEvent>();
      // just pass through any custom events
      yield { event, message, messages };
    }
  }
}
