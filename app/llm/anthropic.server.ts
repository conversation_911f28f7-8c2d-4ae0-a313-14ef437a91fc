import Anthropic from "@anthropic-ai/sdk";
import { envSecret } from "~/env.server";
import { Assert, getSchemaErrors, hv, logify } from "~/Tools";
import { Tool } from "@anthropic-ai/sdk/resources/index.mjs";

export const anthropicClient = new Anthropic({
  apiKey: envSecret.ANTHROPIC_API_KEY,
});

export const sonnetMaxOutputTokens = 8192; // https://docs.anthropic.com/en/docs/about-claude/models
export const CLAUDE_LATEST_MODEL = "claude-sonnet-4-20250514";

export async function invokeAnthropicRulesModel({
  systemPrompt,
  userPrompt,
  tool,
  forceToolChoice = true,
}: {
  userPrompt: string;
  systemPrompt: string;
  tool: Tool;
  forceToolChoice?: boolean;
}) {
  const message = await anthropicClient.messages.create({
    max_tokens: sonnetMaxOutputTokens,
    temperature: 0,
    tools: [tool],
    tool_choice: forceToolChoice
      ? { name: tool.name, type: "tool" }
      : undefined,
    system: [
      {
        text: `${systemPrompt}`,
        type: "text",
        cache_control: { type: "ephemeral" },
      },
    ],
    messages: [{ role: "user", content: userPrompt }],
    model: CLAUDE_LATEST_MODEL,
  });

  logify(message);

  const messageContent = message.content[0];
  Assert.hard(messageContent?.type === "tool_use", "invalid Anthropic return");
  const jsonResult = messageContent.input;

  const schemaErrors = getSchemaErrors(jsonResult, tool.input_schema);

  Assert.hard(!hv(schemaErrors), "Invalid Anthropic return"); // should be impossible (Feb 2025)

  return jsonResult;
}
