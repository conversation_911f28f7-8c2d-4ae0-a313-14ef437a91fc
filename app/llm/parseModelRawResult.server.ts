import { Assert, hv } from "~/Tools";
import { UnderwritingSchemaParsedType } from "./langchain-model.server";
import { RecommendedAction } from "./mainPrompt.server";
import { RuleCategory } from "prisma/interfaces";
import { riskThresholdPct } from "./config";

export type ClientAiBreakdown = Pick<
  UnderwritingSchemaParsedType,
  | "adversarialContent"
  | "ruleMatches"
  | "unmatchedCoverageContent"
  | "inscrutableAndOtherContent"
>;

const getCreateRecFn = (data: UnderwritingSchemaParsedType) =>
  function createRec({
    recommendedAction,
    confidencePct,
  }: {
    recommendedAction: RecommendedAction;
    confidencePct: number;
  }) {
    const didAiRecMatchCode =
      recommendedAction === data.finalRecommendation.recommendedAction;
    return {
      confidencePct,
      recommendedAction,
      aiReasoning: didAiRecMatchCode
        ? data.finalRecommendation.reasoningText
        : undefined,
    };
  };

export function parseModelRawResult(data: UnderwritingSchemaParsedType) {
  const createRec = getCreateRecFn(data);
  if (hv(data.adversarialContent)) {
    return createRec({
      recommendedAction: "OTHER_EVALUATION",
      confidencePct: data.adversarialContent.confidencePercent,
    });
  }

  if (hasRuleMatch(data, "EXCLUDE")) {
    const failMatches = data.ruleMatches.filter(
      m => m.ruleCategory === "EXCLUDE",
    );
    const minFailConfidence = getMinConfidence(failMatches);

    return createRec({
      recommendedAction: "DECLINE",
      confidencePct: minFailConfidence,
    });
  }

  if (hasRuleMatch(data, "REQUIRES_UNDERWRITER_REVIEW")) {
    const reviewMatches = data.ruleMatches.filter(
      m => m.ruleCategory === "REQUIRES_UNDERWRITER_REVIEW",
    );

    return createRec({
      recommendedAction: "RULE_EVALUATION",
      confidencePct: getMinConfidence(reviewMatches),
    });
  }

  // Claude is big on putting Risk content at high priority, very difficult
  // or impossible to change this via prompt so we adjust here which is fine.
  if (hv(data.unmatchedCoverageContent)) {
    const riskyContent = data.unmatchedCoverageContent.filter(
      c => c.riskPercent >= riskThresholdPct,
    );

    if (riskyContent.length > 0) {
      return createRec({
        recommendedAction: "RISK_EVALUATION",
        confidencePct: getMinConfidence(riskyContent),
      });
    }
    // Lower than threshold (10%) risk is considered irrelevant
  }

  const heightenedRiskRules = (data.ruleMatches ?? []).filter(
    m => m.additionalRiskPercent >= riskThresholdPct,
  );

  if (heightenedRiskRules.length > 0) {
    return createRec({
      recommendedAction: "RISK_EVALUATION",
      confidencePct: getMinConfidence(heightenedRiskRules),
    });
  }

  if (hv(data.inscrutableAndOtherContent)) {
    return createRec({
      recommendedAction: "CONTENT_EVALUATION",
      confidencePct: getMinConfidence(data.inscrutableAndOtherContent),
    });
  }

  if (!hv(data.ruleMatches)) {
    // Appears to have no actionable content.  Unusual submission
    return createRec({
      recommendedAction: "OTHER_EVALUATION",
      confidencePct: 100,
    });
  }

  if (
    hasRuleMatch(data, "FLAT_PREMIUM") ||
    hasRuleMatch(data, "PERCENT_PREMIUM")
  ) {
    const raisePremiumRuleCategories: RuleCategory[] = [
      "FLAT_PREMIUM",
      "PERCENT_PREMIUM",
    ];
    const raisePremiumRules = data.ruleMatches.filter(m =>
      raisePremiumRuleCategories.includes(m.ruleCategory),
    );

    return createRec({
      recommendedAction: "RAISE_PREMIUM",
      confidencePct: getMinConfidence(raisePremiumRules),
    });
  }

  const passRules = data.ruleMatches.filter(r => r.ruleCategory === "INCLUDE");
  Assert.soft(passRules.length > 0, "Expected to find PASS rules");

  return createRec({
    recommendedAction: "APPROVE",
    confidencePct: getMinConfidence(passRules),
  });
}

function getMinConfidence(arr: { confidencePercent: number }[]) {
  return Math.min(...arr.map(el => el.confidencePercent));
}

function hasRuleMatch(
  data: UnderwritingSchemaParsedType,
  ruleCategory: RuleCategory,
): data is UnderwritingSchemaParsedType & {
  ruleMatches: NonNullable<
    Required<UnderwritingSchemaParsedType>["ruleMatches"]
  >;
} {
  return Boolean(data.ruleMatches?.some(m => m.ruleCategory === ruleCategory));
}
