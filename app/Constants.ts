import {
  FlatMultiplierType,
  PercentMultiplierType,
  RuleCategory,
} from "prisma/interfaces";
import { BadgeProps } from "@radix-ui/themes";

export type BadgeColor = BadgeProps["color"];

export const CategoryBadgeColor: Record<RuleCategory, BadgeColor> = {
  INCLUDE: "green",
  EXCLUDE: "red",
  FLAT_PREMIUM: "blue",
  PERCENT_PREMIUM: "blue",
  REQUIRES_UNDERWRITER_REVIEW: "orange",
};

export const RuleUiStrings: Record<RuleCategory, string> = {
  INCLUDE: "Include",
  EXCLUDE: "Exclude",
  FLAT_PREMIUM: "Flat Premium",
  PERCENT_PREMIUM: "Percent Premium",
  REQUIRES_UNDERWRITER_REVIEW: "Requires Underwriter Review",
};

// This order can be changed - used in UI.  Numbers should be in order
export const RuleSortMap: Record<RuleCategory, number> = {
  INCLUDE: 0,
  EXCLUDE: 1,
  REQUIRES_UNDERWRITER_REVIEW: 2,
  FLAT_PREMIUM: 3,
  PERCENT_PREMIUM: 4,
};

export const percentMultiplierTypeInfo: Record<
  PercentMultiplierType,
  { uiString: string }
> = {
  BASE_PREMIUM: {
    uiString: "Base Premium",
  },
  TOTAL_REVENUES: {
    uiString: "Total Revenues",
  },
  PARTICIPANT_FEES: {
    uiString: "Participant Fees",
  },
  EQUIPMENT_COSTS: {
    uiString: "Equipment Costs",
  },
};

export const flatMultplierTypeInfo: Record<
  FlatMultiplierType,
  { uiString: string }
> = {
  PER_PARTICIPANT: {
    uiString: "Per Participant",
  },
  PER_DAY: {
    uiString: "Per Day",
  },
  PER_WEEK: {
    uiString: "Per Week",
  },
  ONE_TIME: {
    uiString: "One Time",
  },
};

export const DEBUG_DISPLAY_TOOL_CALLS = false;
