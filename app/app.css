@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap");

.radix-themes:not(.dark, .dark-theme) {
  --color-background: rgb(237 238 243);
}

/* emulates the colors generated from https://www.radix-ui.com/colors/custom in CSS */
/* need column select for editing */
/* prettier-ignore */
.radix-themes:not(.dark, .dark-theme) {
  /* primary accent */
  --primary-9: var(--custom-primary-color);
  /* backgrounds */
  --primary-1: color-mix(in oklab, var(--color-background) 99%, oklch(from var(--primary-9) 50% calc(2.5 * c) h));
  --primary-2: color-mix(in oklab, var(--color-background) 97%, oklch(from var(--primary-9) 50% calc(2.5 * c) h));
  --primary-3: color-mix(in oklab, var(--color-background) 94%, oklch(from var(--primary-9) 50% calc(2.5 * c) h));
  --primary-4: color-mix(in oklab, var(--color-background) 90%, oklch(from var(--primary-9) 50% calc(2.5 * c) h));
  --primary-5: color-mix(in oklab, var(--color-background) 85%, oklch(from var(--primary-9) 50% calc(2.5 * c) h));
  --primary-6: color-mix(in oklab, var(--color-background) 79%, oklch(from var(--primary-9) 50% calc(2.5 * c) h));
  --primary-7: color-mix(in oklab, var(--color-background) 72%, oklch(from var(--primary-9) 50% calc(2.5 * c) h));
  --primary-8: color-mix(in oklab, var(--color-background) 64%, oklch(from var(--primary-9) 50% calc(2.5 * c) h));
  /* hover */
  --primary-10: oklch(from var(--primary-9) calc(0.95 * l) c h);
  /* text */
  --primary-11: oklch(from var(--primary-9) 50% c h);
  --primary-12: oklch(from var(--primary-9) 20% c h);

  --color-background-a0: rgb(from var(--color-background) r g b / 0);
  --primary-a1: color-mix(in oklab, var(--color-background-a0) 99%, oklch(from var(--primary-9) 50% calc(2.5 * c) h));
  --primary-a2: color-mix(in oklab, var(--color-background-a0) 97%, oklch(from var(--primary-9) 50% calc(2.5 * c) h));
  --primary-a3: color-mix(in oklab, var(--color-background-a0) 94%, oklch(from var(--primary-9) 50% calc(2.5 * c) h));
  --primary-a4: color-mix(in oklab, var(--color-background-a0) 90%, oklch(from var(--primary-9) 50% calc(2.5 * c) h));
  --primary-a5: color-mix(in oklab, var(--color-background-a0) 85%, oklch(from var(--primary-9) 50% calc(2.5 * c) h));
  --primary-a6: color-mix(in oklab, var(--color-background-a0) 79%, oklch(from var(--primary-9) 50% calc(2.5 * c) h));
  --primary-a7: color-mix(in oklab, var(--color-background-a0) 72%, oklch(from var(--primary-9) 50% calc(2.5 * c) h));
  --primary-a8: color-mix(in oklab, var(--color-background-a0) 64%, oklch(from var(--primary-9) 50% calc(2.5 * c) h));
  --primary-a9: var(--primary-9);
  --primary-a10: var(--primary-10);
  --primary-a11: var(--primary-11);
  --primary-a12: var(--primary-12);

  /* button text */
  --primary-contrast: oklch(from var(--primary-9) calc(10000 * (0.8 - l)) 0 0);
  --primary-surface: color-mix(in oklab, var(--color-background) 80%, var(--primary-a2));
  --primary-indicator: var(--primary-9);
  --primary-track: var(--primary-9);
}

/* need column select for editing */
/* prettier-ignore */
.radix-themes.dark,
.radix-themes.dark-theme {
  /* primary accent */
  --primary-9: oklch(from var(--custom-primary-color) calc(max(l, 0.6)) calc(1.5 * c) h);
  /* backgrounds */
  --primary-1: color-mix(in oklab, var(--color-background) 97%, oklch(from var(--primary-9) 80% calc(2 * c) h));
  --primary-2: color-mix(in oklab, var(--color-background) 94%, oklch(from var(--primary-9) 80% calc(2 * c) h));
  --primary-3: color-mix(in oklab, var(--color-background) 90%, oklch(from var(--primary-9) 80% calc(2 * c) h));
  --primary-4: color-mix(in oklab, var(--color-background) 85%, oklch(from var(--primary-9) 80% calc(2 * c) h));
  --primary-5: color-mix(in oklab, var(--color-background) 79%, oklch(from var(--primary-9) 80% calc(2 * c) h));
  --primary-6: color-mix(in oklab, var(--color-background) 72%, oklch(from var(--primary-9) 80% calc(2 * c) h));
  --primary-7: color-mix(in oklab, var(--color-background) 64%, oklch(from var(--primary-9) 80% calc(2 * c) h));
  --primary-8: color-mix(in oklab, var(--color-background) 55%, oklch(from var(--primary-9) 80% calc(2 * c) h));
  /* hover */
  --primary-10: oklch(from var(--primary-9) calc(0.95 * l) c h);
  /* text */
  --primary-11: oklch(from var(--primary-9) 80% calc(min(c, 0.16)) h);
  --primary-12: oklch(from var(--primary-9) 90% calc(min(c, 0.08)) h);

  --color-background-a0: rgb(from var(--color-background) r g b / 0);
  --primary-a1: color-mix(in oklab, var(--color-background-a0) 97%, oklch(from var(--primary-9) 80% calc(2 * c) h));
  --primary-a2: color-mix(in oklab, var(--color-background-a0) 94%, oklch(from var(--primary-9) 80% calc(2 * c) h));
  --primary-a3: color-mix(in oklab, var(--color-background-a0) 90%, oklch(from var(--primary-9) 80% calc(2 * c) h));
  --primary-a4: color-mix(in oklab, var(--color-background-a0) 85%, oklch(from var(--primary-9) 80% calc(2 * c) h));
  --primary-a5: color-mix(in oklab, var(--color-background-a0) 79%, oklch(from var(--primary-9) 80% calc(2 * c) h));
  --primary-a6: color-mix(in oklab, var(--color-background-a0) 72%, oklch(from var(--primary-9) 80% calc(2 * c) h));
  --primary-a7: color-mix(in oklab, var(--color-background-a0) 64%, oklch(from var(--primary-9) 80% calc(2 * c) h));
  --primary-a8: color-mix(in oklab, var(--color-background-a0) 55%, oklch(from var(--primary-9) 80% calc(2 * c) h));
  --primary-a9: var(--primary-9);
  --primary-a10: var(--primary-10);
  --primary-a11: var(--primary-11);
  --primary-a12: var(--primary-12);

  /* button text */
  --primary-contrast: oklch(from var(--primary-9) calc(10000 * (0.8 - l)) 0 0);
  --primary-surface: color-mix(in oklab, var(--color-background) 50%, var(--primary-a2));
  --primary-indicator: var(--primary-9);
  --primary-track: var(--primary-9);
}

.radix-themes {
  --lighter-4: oklab(from var(--color-background) calc(l + 0.15) a b);
  --default-font-family: "Inter", -apple-system, BlinkMacSystemFont,
    "Segoe UI (Custom)", Roboto, "Helvetica Neue", "Open Sans (Custom)",
    system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";

  /* Use level 5 for focus outline instead of level 8 */
  --focus-8: var(--primary-5);
}

[data-accent-color="primary"] {
  --accent-1: var(--primary-1);
  --accent-2: var(--primary-2);
  --accent-3: var(--primary-3);
  --accent-4: var(--primary-4);
  --accent-5: var(--primary-5);
  --accent-6: var(--primary-6);
  --accent-7: var(--primary-7);
  --accent-8: var(--primary-8);
  --accent-9: var(--primary-9);
  --accent-10: var(--primary-10);
  --accent-11: var(--primary-11);
  --accent-12: var(--primary-12);

  --accent-a1: var(--primary-a1);
  --accent-a2: var(--primary-a2);
  --accent-a3: var(--primary-a3);
  --accent-a4: var(--primary-a4);
  --accent-a5: var(--primary-a5);
  --accent-a6: var(--primary-a6);
  --accent-a7: var(--primary-a7);
  --accent-a8: var(--primary-a8);
  --accent-a9: var(--primary-a9);
  --accent-a10: var(--primary-a10);
  --accent-a11: var(--primary-a11);
  --accent-a12: var(--primary-a12);

  --accent-contrast: var(--primary-contrast);
  --accent-surface: var(--primary-surface);
  --accent-indicator: var(--primary-indicator);
  --accent-track: var(--primary-track);
}
