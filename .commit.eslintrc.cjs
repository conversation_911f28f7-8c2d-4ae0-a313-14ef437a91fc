/** @type {import('eslint').Linter.Config} */
module.exports = {
  extends: ["./.eslintrc.cjs"],
  plugins: ["unused-imports"],
  overrides: [
    // Typescript
    {
      files: ["**/*.{ts,tsx}"],
      rules: {
        "import/order": [
          0,
          {
            groups: [
              "unknown",
              ["external", "builtin", "internal"],
              "parent",
              "index",
              "sibling",
            ],
            "newlines-between": "never",
            alphabetize: { order: "asc", caseInsensitive: true },
          },
        ],
        "unused-imports/no-unused-imports": "error",
        "unused-imports/no-unused-vars": [
          "error",
          {
            vars: "all",
            varsIgnorePattern: "^_",
            args: "all",
            argsIgnorePattern: "^_",
            ignoreRestSiblings: true,
          },
        ],
        "no-useless-rename": "error",

        // covered by TS / IDE highlighting
        "@typescript-eslint/no-unused-vars": "off",
        // gets confused by react-router file based routing, and this error is caught by TS anyway
        "import/no-unresolved": "off",
      },
    },
  ],
};
