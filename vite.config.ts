import { reactRouter } from "@react-router/dev/vite";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";

const IS_HMR_ENABLED = true;

export default defineConfig({
  esbuild: {
    supported: { "top-level-await": true },
  },
  server: {
    hmr: IS_HMR_ENABLED,
  },
  plugins: [reactRouter(), tsconfigPaths()],
  resolve: {
    extensions: [
      ".mjs",
      ".js",
      ".mts",
      ".ts",
      ".jsx",
      ".tsx",
      ".json",
      ".node",
    ],
  },
  optimizeDeps: { exclude: ["isolated-vm"] },
});
