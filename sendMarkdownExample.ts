import {
  devEmail,
  emailClient,
  sendMarkdownEmail,
} from "./app/utils/email.server";

async function main() {
  await sendMarkdownEmail(
    emailClient,
    {
      To: devEmail,
      Subject: "UZero Markdown Sample",
    },
    exampleMd,
  );
}

const exampleMd = /*md*/ `# ATX Heading Level 1

## ATX Heading Level 2

### ATX Heading Level 3

#### ATX Heading Level 4

##### ATX Heading Level 5

###### ATX Heading Level 6

Setext Heading Level 1
=======================

Setext Heading Level 2
-----------------------

## Paragraphs and Line Breaks

This is a paragraph. Paragraphs are separated by blank lines.

This is another paragraph. You can create a hard line break by ending a line with two spaces  
or by ending a line with a backslash\\
like this.

## Emphasis

*This text is italic* and _this text is also italic_.

**This text is bold** and __this text is also bold__.

***This text is bold and italic*** and ___this is also bold and italic___.

You can also combine them: **bold with *italic* inside** and *italic with **bold** inside*.

## Code

This is an \`inline code span\` within a paragraph.

Here's a fenced code block:

\`\`\`javascript
function example() {
    console.log("Hello, world!");
    return true;
}
\`\`\`

You can also use tildes for fenced code blocks:

~~~python
def hello_world():
    print("Hello, world!")
    return "success"
~~~

Here's an indented code block:

    // This is an indented code block
    var x = 10;
    var y = 20;
    console.log(x + y);

## Links

This is an [inline link](https://example.com "Optional title").

This is an [inline link without title](https://example.com).

This is a [reference link][ref1].

This is another [reference link][ref2].

You can also use [relative links](../path/to/file.html).

Autolinks: <https://example.com> and <<EMAIL>>

[ref1]: https://example.com "Reference link with title"
[ref2]: https://another-example.com

## Images

Inline image: ![Alt text](https://picsum.photos/150/150 "Image title")

Reference image: ![Alt text][img1]

[img1]: https://picsum.photos/200/200 "Reference image with title"

## Lists

### Unordered Lists

- Item 1
- Item 2
  - Nested item 2.1
  - Nested item 2.2
    - Deeply nested item
- Item 3

* You can use asterisks
* Instead of hyphens
  * For nested items too

+ Or plus signs
+ Work as well
  + With nesting

### Ordered Lists

1. First item
2. Second item
   1. Nested numbered item
   2. Another nested item
      1. Deeply nested
3. Third item

List items can contain multiple paragraphs:

1. First item

   This is a second paragraph in the first item.

2. Second item

   Another paragraph here.

3. Third item

## Blockquotes

> This is a blockquote.
> 
> Blockquotes can span multiple paragraphs.

> Blockquotes can also be nested:
> 
> > This is a nested blockquote.
> > 
> > > And even deeper nesting.

> You can include other markdown elements in blockquotes:
> 
> ## Heading in blockquote
> 
> 1. Ordered list in blockquote
> 2. Another item
> 
> *Emphasis* and **strong** text work too.

## Horizontal Rules

---

***

___

- - -

* * *

_ _ _

## HTML

You can include raw HTML in markdown:

<div style="color: blue;">
This is a blue div.
</div>

<p>This is an HTML paragraph with <strong>bold</strong> and <em>italic</em> text.</p>

<!-- This is an HTML comment -->

<details>
<summary>Click to expand</summary>
This content is hidden by default.
</details>

## Backslash Escapes

You can escape special characters with backslashes:

\\*Not italic\\* and \\*\\*not bold**

\\# Not a heading

\\[Not a link\\](example.com)

\\\`Not code\\\`

## Entity References

&copy; &amp; &lt; &gt; &quot; &#35; &#x23;

Named entities: &nbsp; &mdash; &ldquo; &rdquo;

## Complex Examples

### Code block in blockquote

> Here's how you might use a function:
> 
> \`\`\`javascript
> function greet(name) {
>     return \`Hello, \${name}!\`;
> }
> \`\`\`

### List with code and links

1. Install the package: \`npm install example-package\`
2. Read the [documentation](https://example.com/docs)
3. Run the following code:
   
   \`\`\`bash
   npm start
   \`\`\`

### Nested list with blockquotes

- First level
  - Second level
    > This is a blockquote inside a list item.
    > 
    > It can span multiple lines.
  - Another second level item
- Back to first level

## Mixed Content Example

**Important**: This section demonstrates how different markdown elements can be combined.

1. **Step 1**: Visit <https://example.com>
2. **Step 2**: Copy this code:
   
   \`\`\`html
   <!DOCTYPE html>
   <html>
   <head>
       <title>Example</title>
   </head>
   <body>
       <h1>Hello, World!</h1>
   </body>
   </html>
   \`\`\`

3. **Step 3**: Save it as \`index.html\`

> **Note**: Make sure to validate your HTML using an [online validator](https://validator.w3.org/).`;

await main();
